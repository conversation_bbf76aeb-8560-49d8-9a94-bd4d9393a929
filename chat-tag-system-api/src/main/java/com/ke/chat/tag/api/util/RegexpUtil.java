package com.ke.chat.tag.api.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RegexpUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(RegexpUtil.class);
    /**
     * 全角转半角
     * 菁优网填空题答案在题干中，前后用 12288 包围。为了适配，放行 12288，即 \u3000
     *
     * @param input String.
     * @return 半角字符串
     */
    public static String toDBC(String input) {

        List<Character> excludeChars = Lists.newArrayList(
            '．', '。', '：', '，', '、', '（', '）', '【', '】', '「', '」');
        char[] c = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (!excludeChars.contains(c[i]) && c[i] > '\uFF00' && c[i] < '\uFF5F') {
                c[i] = (char) (c[i] - 65248);
            }
        }
        return new String(c);
    }

    /**
     * 包含所有不可见字符
     */
    public static String removeInvisibleWhiteSpaces(String s) {

        if (Strings.isNullOrEmpty(s)) {
            return s;
        }
        return s.replaceAll("[\\p{C}\\p{Z}]", "");
    }

    public static List<String> splitByRegexp(String content, String regexp) {
        Pattern pattern = Pattern.compile(regexp);
        return Lists.newArrayList(Splitter.on(pattern).omitEmptyStrings().trimResults().split(content));
    }

    /**
     * 找出匹配分组
     */
    public static PatternMatchResult matchGroups(String regex, String str) {

        if (Strings.isNullOrEmpty(str)) {
            return PatternMatchResult.unmatchResult();
        }

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);

        List<List<String>> groups = Lists.newArrayList();
        List<int[]> index = Lists.newArrayList();
        int count = matcher.groupCount();
        while (matcher.find()) {
            String fullStr = matcher.group();
            if (Strings.isNullOrEmpty(fullStr)) {
                // 不要空串
                continue;
            }

            index.add(new int[]{matcher.start(), matcher.end()});
            List<String> group = Lists.newArrayList(fullStr);
            for (int i = 0; i < count; i++) {
                group.add(matcher.group(i + 1));
            }
            groups.add(group);
        }

        return PatternMatchResult.create(matcher.matches(), matcher.lookingAt(), groups, index);
    }

    @Setter
    @Getter
    public static class PatternMatchResult {

        /**
         * 是否整个字符串都匹配
         */
        private boolean matchAll;

        /**
         * 是否字符串前缀都匹配
         */
        private boolean matchPrefix;

        /**
         * 找到的匹配组
         * [[全量匹配的字符串，匹配组list...],...]
         */
        private List<List<String>> groups;

        /**
         * 匹配组字符串的前后索引
         */
        private List<int[]> index;

        /**
         * 是否有匹配到对应的分组
         */
        public boolean iFind() {
            return groups != null && groups.size() > 0;
        }

        public static PatternMatchResult unmatchResult() {
            PatternMatchResult result = new PatternMatchResult();
            result.matchAll = false;
            result.matchPrefix = false;
            result.groups = null;
            result.index = null;
            return result;
        }

        public static PatternMatchResult create(boolean matchAll, boolean matchPrefix, List<List<String>> groups, List<int[]> index) {
            PatternMatchResult result = new PatternMatchResult();
            result.matchAll = matchAll;
            result.matchPrefix = matchPrefix;
            result.groups = groups;
            result.index = index;
            return result;
        }

        /**
         * 获取默认匹配的分组的值，即第一组匹配的分组的值
         */
        public String defaultGroupValue(int idx) {
            if (groups != null && groups.size() > 0 && groups.get(0) != null && groups.get(0).size() > idx) {
                return groups.get(0).get(idx);
            }
            return null;
        }
    }

    public static void main(String[] args) {
        String content = "财务运营中心费控报销Q&A\\n结合近期报销常见问题，及热线高频问题，本期总结了如下Q&A：\\nQ1：无票报销水电及物业费的填单注意事项有哪些？\\nA：基本信息区处，是否例外事项选择“是”，例外事项处根据金额选择物业能源无票报销，报销明细区的是否无票选择“无票”，发票类型选择其他，样图如下：\\nQ2：无票报销水电及物业费的填单注意事项有哪些？\\nA：基本信息区处，是否例外事项选择“是”，例外事项处根据金额选择物业能源无票报销，报销明细区的是否无票选择“无票”，发票类型选择其他，样图如下：\\nQ3：无票报销水电及物业费的填单注意事项有哪些？\\nA：基本信息区处，是否例外事项选择“是”，例外事项处根据金额选择物业能源无票报销，报销明细区的是否无票选择“无票”，发票类型选择其他，样图如下：\\nQ4：无票报销水电及物业费的填单注意事项有哪些？\\nA：基本信息区处，是否例外事项选择“是”，例外事项处根据金额选择物业能源无票报销，报销明细区的是否无票选择“无票”，发票类型选择其他，样图如下：\\n";
//		String regexp_match = "^(.*?)(Q(\\d)*[:：].*?A[:：].*?)*$";
        String regexp_match = "(Q\\d*[:：].*?A[:：].*?)";
        PatternMatchResult result = matchGroups(regexp_match, content);
        System.out.println("OK");
    }

}
