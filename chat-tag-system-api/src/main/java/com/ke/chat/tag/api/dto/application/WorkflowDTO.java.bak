package com.ke.chat.tag.api.dto.application;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ke.chat.tag.api.enums.application.ApplicationMode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.Arrays;

public class WorkflowDTO {

    @Getter
    @AllArgsConstructor
    public enum WorkflowMode {
        WORKFLOW("workflow"), ADVANCED_CHAT("advanced-chat");
        final String name;

        public ApplicationMode mappingAssistantMode() {
            return WORKFLOW.equals(this) ? ApplicationMode.LLM : ApplicationMode.CHATFLOW;
        }

        public static WorkflowMode of(String mode) {
            return Arrays.stream(WorkflowMode.values()).filter(m -> m.getName().equals(mode)).findFirst().orElseThrow(() -> new IllegalArgumentException("unknown mode: " + mode));
        }
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @SuperBuilder(toBuilder = true)
    @Data
    public static class Workflow {
        /**
         * 租户id
         */
        private String tenantId;

        private String workflowId;

        private String title;

        private String mode;

        private String desc;

        /**
         * 工作流DAG配置
         */
        private String graph;

        /**
         * 工作流版本，0: draft, >0 正式版时间戳
         */
        private Long version;

        private Long cuid;

        private String cuName;

        private LocalDateTime ctime;

        private Long muid;

        private String muName;

        private LocalDateTime mtime;

        private String status;
    }

    @EqualsAndHashCode(callSuper = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @SuperBuilder(toBuilder = true)
    @Data
    public static class WorkflowWithHasPublished extends Workflow {
        private String spaceCode;
        private Long latestPublishVersion;
        @JsonProperty("isOwnerUser")
        private boolean ownerUser;
        private boolean existInvalidBind;

        public Boolean getHasPublished() {
            return latestPublishVersion != null && latestPublishVersion > 0;
        }
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DifyApp {
        String tenantId;
        String id;
        String name;
        String description;
        @Builder.Default
        String mode = "advanced-chat";
        @Builder.Default
        String icon = "\ud83e\udd16";
        @Builder.Default
        String icon_background = "#FFEAD5";
        boolean enable_site;
        @Builder.Default
        boolean enable_api = true;
        Object model_config;
        @Builder.Default
        Site site = new Site();
        String api_base_url;
        int created_at;
        @Builder.Default
        Object[] deleted_tools = new Object[0];
        @Builder.Default
        Object[] tags = new Object[0];
        String space_code;
        Long cuid;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Site {
            String access_token;
            String code;
            String title;
            Object icon;
            Object icon_background;
            Object description;
            String default_language;
            Object customize_domain;
            Object copyright;
            Object privacy_policy;
            Object custom_disclaimer;
            String customize_token_strategy;
            boolean prompt_public;
            String app_base_url;
        }
    }

}
