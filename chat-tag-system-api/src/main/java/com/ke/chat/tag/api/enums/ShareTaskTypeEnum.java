package com.ke.chat.tag.api.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/3/10 6:22 下午
 **/
public enum ShareTaskTypeEnum {
    CONVERSATION(0, "会话"),
    SPACE(1, "知识库");

    public final int code;
    public final String message;

    ShareTaskTypeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ShareTaskTypeEnum getByCode(int code) {
        return Arrays.stream(ShareTaskTypeEnum.values())
                .filter(i -> i.code == code)
                .findFirst().orElse(null);
    }
}
