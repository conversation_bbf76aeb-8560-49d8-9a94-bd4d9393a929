package com.ke.chat.tag.api.enums.audio;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 回答结果状态枚举
 * @date 2023/8/8 17:51
 */
@Getter
@AllArgsConstructor
public enum ResponseStatusEnum {
    SUCCESS(0, "成功"),
    PROCESSING(1, "处理中"),
    FINISHED(2, "结束"),
    FAILED(3, "失败");

    private final Integer code;
    private final String desc;
}
