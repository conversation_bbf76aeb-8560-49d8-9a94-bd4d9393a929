package com.ke.chat.tag.api.enums.audio;


public enum EventEnum {
    UNKNOWN("unknown", "未知"),
    DEFAULT("default", "默认"),
    INPUT_AUDIO_BUFFER_APPEND("input_audio_buffer_append", "输入音频缓冲追加"),
    INPUT_AUDIO_BUFFER_COMMIT("input_audio_buffer_commit", "输入音频缓冲提交"),
    RESPONSE_CANCEL("response_cancel", "响应取消"),
    RESPONSE_CREATE("response_create", "创建响应"),
    SESSION_UPDATE("session_update", "会话更新"),
    CONVERSATION_ITEM_CREATE("conversation_item_create", "对话项创建"),
    CONVERSATION_ITEM_TRUNCATE("conversation_item_truncate", "对话项截断"),
    ERROR("error", "错误"),
    SESSION_CREATED("session_created", "会话已创建"),
    SESSION_UPDATED("session_updated", "会话已更新"),
    CONVERSATION_CREATED("conversation_created", "对话已创建"),
    CONVERSATION_ITEM_CREATED("conversation_item_created", "对话项已创建"),
    CONVERSATION_ITEM_TRUNCATED("conversation_item_truncated", "对话项已截断"),
    INPUT_AUDIO_BUFFER_COMMITTED("input_audio_buffer_committed", "输入音频缓冲已提交"),
    INPUT_AUDIO_TRANSCRIPT_DELTA("input_audio_transcript_delta", "输入音频转录增量"),
    INPUT_AUDIO_TRANSCRIPT_DONE("input_audio_transcript_done", "输入音频转录完成"),
    CONVERSATION_ITEM_INPUT_AUDIO_TRANSCRIPTION_COMPLETED("conversation_item_input_audio_transcription_completed", "对话项输入音频转录已完成"),
    RESPONSE_CREATED("response_created", "响应已创建"),
    RESPONSE_TEXT_DELTA("response_text_delta", "响应文本增量"),
    RESPONSE_TEXT_DONE("response_text_done", "响应文本完成"),
    RESPONSE_AUDIO_DELTA("response_audio_delta", "响应音频增量"),
    RESPONSE_AUDIO_DONE("response_audio_done", "响应音频完成"),
    RESPONSE_AUDIO_TRANSCRIPT_DELTA("response_audio_transcript_delta", "响应音频转录增量"),
    RESPONSE_AUDIO_TRANSCRIPT_DONE("response_audio_transcript_done", "响应音频转录完成");

    private final String code;
    private final String desc;

    EventEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
