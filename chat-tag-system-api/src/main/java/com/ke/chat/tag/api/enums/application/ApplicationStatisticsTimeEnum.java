package com.ke.chat.tag.api.enums.application;

import lombok.Getter;
import lombok.AllArgsConstructor;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum ApplicationStatisticsTimeEnum {
    UNKNOWN("unknown", "未知");

    private final String code;
    private final String desc;
}
