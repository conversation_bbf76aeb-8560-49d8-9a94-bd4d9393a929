package com.ke.chat.tag.api.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 **/
public enum ConversationStatusEnum {
    OPEN(0, "开启"),
    CLOSED(1, "关闭");

    public final int code;
    public final String message;

    ConversationStatusEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ConversationStatusEnum getByCode(int code) {
        return Arrays.stream(ConversationStatusEnum.values())
                .filter(i -> i.code == code)
                .findFirst().orElse(OPEN);
    }
}
