package com.ke.chat.tag.api.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/3/1 11:11 上午
 **/
public enum AnswerStatusEnum {
    SUCCESS(0, "成功"),
    FAILED(1, "失败"),
    NO_REPLY(2, "未回复"),
    CUT(3, "截断"),
    CANCEL(4, "取消"),
    SENSITIVE(5, "包含敏感内容"),
    RESTRICTED(6, "限流"),
    TIMEOUT(7, "超时");

    public final int code;
    public final String message;

    AnswerStatusEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static AnswerStatusEnum getStatusByCode(String code) {
        return Arrays.stream(AnswerStatusEnum.values())
            .filter(i -> String.valueOf(i.code).equals(code))
            .findFirst().orElse(SUCCESS);
    }

    public static boolean isErrorStatus(int status) {
        return SUCCESS.code != status && CANCEL.code != status
            && SENSITIVE.code != status && RESTRICTED.code != status && TIMEOUT.code != status;
    }

    public static int getDisplayStatus(int status) {
        // 敏感与限流仍展示归类失败
        if (SENSITIVE.code == status || RESTRICTED.code == status) {
            return FAILED.code;
        }
        return status;
    }
}
