package com.ke.chat.tag.api.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/3/1 11:12 上午
 **/
public enum MessageStatusEnum {
    SUCCESS(0, "成功"),
    FAILED(1, "失败"),
    SENDING(2, "发送中"),
    CANCEL(3, "取消");

    public final int code;
    public final String message;

    MessageStatusEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static MessageStatusEnum getStatusByCode(String code) {
        return Arrays.stream(MessageStatusEnum.values())
                .filter(i -> String.valueOf(i.code).equals(code))
                .findFirst().orElse(SUCCESS);
    }

    public static boolean isSuccess(int code) {
        return SUCCESS.code == code;
    }
    
    public static boolean isFailed(int code) {
        return FAILED.code == code;
    }
}
