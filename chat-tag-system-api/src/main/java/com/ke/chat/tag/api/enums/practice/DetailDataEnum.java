package com.ke.chat.tag.api.enums.practice;

import lombok.Getter;
import lombok.AllArgsConstructor;
import com.ke.chat.tag.api.dto.practice.EvaluationManagerDTO;
import com.ke.chat.tag.api.dto.practice.FeedbackManagerDTO;
import com.ke.chat.tag.api.dto.practice.PromptManagerDTO;
import com.ke.chat.tag.api.dto.practice.ResistanceProblemDTO;
import com.ke.chat.tag.api.dto.practice.ScoreManagerDTO;
import com.ke.chat.tag.api.dto.practice.ScriptDescriptionDTO;
import com.ke.chat.tag.api.dto.practice.TtsAudioManagerDTO;
import com.ke.chat.tag.api.dto.practice.challenge.ChallengeDetailCheck;n

@Getter
@AllArgsConstructor
public enum DetailDataEnum {
    UNKNOWN("unknown", "未知");

    private final String code;
    private final String desc;
}
