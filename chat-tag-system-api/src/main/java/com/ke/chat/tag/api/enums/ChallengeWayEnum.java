package com.ke.chat.tag.api.enums;

import lombok.Getter;
import lombok.AllArgsConstructor;
import com.ke.chat.tag.api.dto.practice.ChallengeButtonDTO;
import software.amazon.awssdk.utils.StringUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum ChallengeWayEnum {
    UNKNOWN("unknown", "未知");

    private final String code;
    private final String desc;
}
