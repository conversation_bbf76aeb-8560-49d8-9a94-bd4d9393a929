package com.ke.chat.tag.api.enums.application;

import lombok.Getter;
import lombok.AllArgsConstructor;
import com.ke.chat.tag.api.util.DateUtil;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Objects;n

@Getter
@AllArgsConstructor
public enum UserConversationTip {
    UNKNOWN("unknown", "未知");

    private final String code;
    private final String desc;
}
