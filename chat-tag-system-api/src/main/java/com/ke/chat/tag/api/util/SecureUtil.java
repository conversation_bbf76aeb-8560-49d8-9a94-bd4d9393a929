package com.ke.chat.tag.api.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
/**
 * <AUTHOR>
 * @date 2023/4/21 3:43 下午
 **/

import com.ke.risk.safety.common.util.bigdata.AES128DecodePublicUDF;
import com.ke.risk.safety.common.util.bigdata.AES128EncodePublicUDF;
import org.springframework.util.DigestUtils;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 加密解密工具类
 *
 * <AUTHOR>
 * @date 2022/08/11
 */
public class SecureUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(SecureUtil.class);
    private static final AES128EncodePublicUDF encode = new AES128EncodePublicUDF();

    /**
     * 对字符串进行加密
     *
     * @param data 被加密的字符串
     * @return 加密后的字符串
     */
    public static String encrypt(String data) {
        return encode.evaluate(data);
    }


    private static final AES128DecodePublicUDF decode = new AES128DecodePublicUDF();

    /**
     * 对字符串进行解密
     *
     * @param data 被解密的字符串
     * @return 解密后的字符串
     */
    public static String decrypt(String data) {
        return decode.evaluate(data);
    }

    public static String signatureUrl(String domain, String path, String ak, String sk, String exp) {
        Map<String, String> data = new HashMap<>();
        long time = Calendar.getInstance().getTimeInMillis() / 1000;
        data.put("ak", ak);
        data.put("exp", exp);
        data.put("path", path);
        data.put("ts", "" + time);
        Set<String> keySet = data.keySet();
        List<String> list = new ArrayList<>(keySet);
        Collections.sort(list);
        StringBuilder verifyStr = new StringBuilder();
        for (String s : list) {
            verifyStr.append(s.trim()).append("=").append(data.get(s).trim()).append("&");
        }
        verifyStr.append("sk=").append(sk);
        String sign = DigestUtils.md5DigestAsHex(verifyStr.toString().getBytes());
        String params = "ak=" + data.get("ak") + "&exp=" + data.get("exp") + "&ts=" + data.get("ts") + "&sign=" + sign;

        return domain + path + '?' + params;
    }


    private SecureUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static String genSignatureUrl(String originUrl, String ak, String sk) throws MalformedURLException {
        URL url = new URL(originUrl);
        String domain = url.getProtocol() + "://" + url.getHost();
        String path = url.getPath();
        return SecureUtil.signatureUrl(domain, path, ak, sk, "1800");
    }

}
