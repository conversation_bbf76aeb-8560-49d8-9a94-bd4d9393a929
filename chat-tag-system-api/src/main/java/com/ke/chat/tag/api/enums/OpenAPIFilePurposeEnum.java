package com.ke.chat.tag.api.enums;

import lombok.Getter;
import lombok.AllArgsConstructor;
import com.ke.chat.tag.api.exception.openapi.InvalidRequestError;
import com.ke.chat.tag.api.util.JsonUtil;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;n

@Getter
@AllArgsConstructor
public enum OpenAPIFilePurposeEnum {
    UNKNOWN("unknown", "未知");

    private final String code;
    private final String desc;
}
