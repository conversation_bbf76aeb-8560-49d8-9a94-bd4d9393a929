#!/bin/bash

# 修复枚举文件中import语句末尾多余的字符 'n'
# 这个脚本会查找所有Java文件中以 'n' 结尾的import语句并修复它们

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

info "开始修复枚举文件中的import语句错误..."

# 查找所有包含错误import语句的Java文件
FILES_WITH_ERRORS=$(find . -name "*.java" -type f -exec grep -l "import.*n$" {} \; 2>/dev/null || true)

if [ -z "$FILES_WITH_ERRORS" ]; then
    info "没有找到需要修复的文件"
    exit 0
fi

FIXED_COUNT=0
TOTAL_COUNT=0

# 修复每个文件
while IFS= read -r file; do
    if [ -f "$file" ]; then
        TOTAL_COUNT=$((TOTAL_COUNT + 1))
        info "修复文件: $file"
        
        # 使用sed修复import语句末尾的 'n'
        if sed -i.bak 's/import\(.*\)n$/import\1/g' "$file"; then
            # 检查是否真的有修改
            if ! diff -q "$file" "$file.bak" > /dev/null 2>&1; then
                FIXED_COUNT=$((FIXED_COUNT + 1))
                info "  ✓ 已修复"
                # 删除备份文件
                rm -f "$file.bak"
            else
                warn "  - 无需修复"
                rm -f "$file.bak"
            fi
        else
            error "  ✗ 修复失败"
            # 恢复备份文件
            if [ -f "$file.bak" ]; then
                mv "$file.bak" "$file"
            fi
        fi
    fi
done <<< "$FILES_WITH_ERRORS"

info "修复完成！"
info "总共检查了 $TOTAL_COUNT 个文件"
info "成功修复了 $FIXED_COUNT 个文件"

# 验证修复结果
info "验证修复结果..."
REMAINING_ERRORS=$(find . -name "*.java" -type f -exec grep -l "import.*n$" {} \; 2>/dev/null | wc -l || echo "0")

if [ "$REMAINING_ERRORS" -eq 0 ]; then
    info "✓ 所有import语句错误已修复"
else
    warn "⚠ 仍有 $REMAINING_ERRORS 个文件存在问题"
fi

info "现在可以尝试重新编译项目"
