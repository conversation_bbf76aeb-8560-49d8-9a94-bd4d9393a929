#!/bin/bash

# 修复编译错误的综合脚本
# 这个脚本会修复多种类型的编译错误

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

info "开始修复编译错误..."

# 1. 修复缺少Logger import的工具类
info "修复缺少Logger import的工具类..."
UTIL_FILES=$(find . -name "*Util.java" -type f)
for file in $UTIL_FILES; do
    if [ -f "$file" ]; then
        # 检查是否使用了Logger但没有import
        if grep -q "Logger" "$file" && ! grep -q "import.*Logger" "$file"; then
            info "修复文件: $file"
            # 在package语句后添加import
            sed -i.bak '/^package /a\
\
import org.slf4j.Logger;\
import org.slf4j.LoggerFactory;' "$file"
            rm -f "$file.bak"
        fi
    fi
done

# 2. 修复枚举类构造函数问题
info "修复枚举类构造函数问题..."
ENUM_FILES=$(find . -name "*Enum.java" -type f)
for file in $ENUM_FILES; do
    if [ -f "$file" ]; then
        # 检查是否有枚举值但没有构造函数
        if grep -q "(" "$file" && grep -q "private final" "$file" && ! grep -q "AllArgsConstructor" "$file"; then
            info "修复枚举文件: $file"
            # 在import部分添加@AllArgsConstructor注解的import
            if ! grep -q "import lombok.AllArgsConstructor" "$file"; then
                sed -i.bak '/import lombok.Getter/a\
import lombok.AllArgsConstructor;' "$file"
            fi
            # 在@Getter注解后添加@AllArgsConstructor
            if ! grep -q "@AllArgsConstructor" "$file"; then
                sed -i.bak '/@Getter/a\
@AllArgsConstructor' "$file"
            fi
            rm -f "$file.bak"
        fi
    fi
done

# 3. 修复特定的文件问题
info "修复特定文件问题..."

# 修复CustomDateDeserializer.java文件
CUSTOM_DATE_FILE="./chat-tag-system-api/src/main/java/com/ke/chat/tag/api/vo/partner/CustomDateDeserializer.java"
if [ -f "$CUSTOM_DATE_FILE" ]; then
    info "修复CustomDateDeserializer.java"
    cat > "$CUSTOM_DATE_FILE" << 'EOF'
package com.ke.chat.tag.api.vo.partner;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class CustomDateDeserializer extends JsonDeserializer<Date> {
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public Date deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        String date = jsonParser.getText();
        try {
            return dateFormat.parse(date);
        } catch (ParseException e) {
            throw new IOException("Unable to parse date: " + date, e);
        }
    }
}
EOF
fi

info "编译错误修复完成！"
info "现在可以尝试重新编译项目"
