#!/bin/bash

# 修复枚举类构造函数问题的脚本
# 这个脚本会为所有有参数的枚举类添加手动构造函数，替代Lombok注解

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

info "开始修复枚举类构造函数问题..."

# 查找所有枚举文件
ENUM_FILES=$(find . -name "*Enum.java" -type f)

FIXED_COUNT=0
TOTAL_COUNT=0

for file in $ENUM_FILES; do
    if [ -f "$file" ]; then
        TOTAL_COUNT=$((TOTAL_COUNT + 1))
        
        # 检查是否有枚举值带参数但没有构造函数
        if grep -q "(" "$file" && grep -q "private final" "$file" && ! grep -q "Enum(" "$file"; then
            info "修复枚举文件: $file"
            
            # 备份文件
            cp "$file" "$file.bak"
            
            # 获取枚举类名
            ENUM_NAME=$(basename "$file" .java)
            
            # 检查字段类型和数量
            FIELD_COUNT=$(grep -c "private final" "$file" || echo "0")
            
            if [ "$FIELD_COUNT" -eq 2 ]; then
                # 假设是两个String字段的情况
                FIRST_FIELD=$(grep "private final" "$file" | head -1 | awk '{print $4}' | sed 's/;//')
                SECOND_FIELD=$(grep "private final" "$file" | tail -1 | awk '{print $4}' | sed 's/;//')
                
                # 在最后一个字段后添加构造函数和getter方法
                sed -i.tmp "/private final.*${SECOND_FIELD};/a\\
\\
    ${ENUM_NAME}(String ${FIRST_FIELD}, String ${SECOND_FIELD}) {\\
        this.${FIRST_FIELD} = ${FIRST_FIELD};\\
        this.${SECOND_FIELD} = ${SECOND_FIELD};\\
    }\\
\\
    public String get$(echo ${FIRST_FIELD} | sed 's/^./\U&/')() {\\
        return ${FIRST_FIELD};\\
    }\\
\\
    public String get$(echo ${SECOND_FIELD} | sed 's/^./\U&/')() {\\
        return ${SECOND_FIELD};\\
    }" "$file"
                
                # 移除临时文件
                rm -f "$file.tmp"
                
                # 移除Lombok注解
                sed -i.tmp '/@Getter/d; /@AllArgsConstructor/d; /import lombok/d' "$file"
                rm -f "$file.tmp"
                
                FIXED_COUNT=$((FIXED_COUNT + 1))
                info "  ✓ 已修复 (2个字段)"
            else
                warn "  - 跳过 (字段数量: $FIELD_COUNT)"
                # 恢复备份
                mv "$file.bak" "$file"
            fi
        else
            info "检查文件: $file - 无需修复"
        fi
    fi
done

info "修复完成！"
info "总共检查了 $TOTAL_COUNT 个枚举文件"
info "成功修复了 $FIXED_COUNT 个文件"

# 清理备份文件
find . -name "*.java.bak" -delete

info "现在可以尝试重新编译项目"
