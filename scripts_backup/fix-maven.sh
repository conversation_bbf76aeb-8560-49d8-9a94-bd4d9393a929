#!/bin/bash

# 创建正确的Maven设置文件，手动修复XML标签问题
cat > maven_settings.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0">
  <mirrors>
    <mirror>
      <id>lianjia-group-mirror</id>
      <mirrorOf>central</mirrorOf>
      <name>Lianjia Group Repository</name>
      <url>http://maven.lianjia.com/content/groups/Lianjia-Group</url>
    </mirror>
  </mirrors>
  <profiles>
    <profile>
      <id>lianjia-profile</id>
      <repositories>
        <repository>
          <id>lianjia-group</id>
          <name>Lianjia Group Repository</name>
          <url>http://maven.lianjia.com/content/groups/Lianjia-Group</url>
          <releases><enabled>true</enabled></releases>
          <snapshots><enabled>true</enabled></snapshots>
        </repository>
      </repositories>
      <pluginRepositories>
        <pluginRepository>
          <id>lianjia-group</id>
          <name>Lianjia Group Repository</name>
          <url>http://maven.lianjia.com/content/groups/Lianjia-Group</url>
          <releases><enabled>true</enabled></releases>
          <snapshots><enabled>true</enabled></snapshots>
        </pluginRepository>
      </pluginRepositories>
    </profile>
    <profile>
      <id>disable-http-blocker</id>
      <properties>
        <maven.resolver.transport.http.blocker.disabled>true</maven.resolver.transport.http.blocker.disabled>
      </properties>
    </profile>
  </profiles>
  <activeProfiles>
    <activeProfile>lianjia-profile</activeProfile>
    <activeProfile>disable-http-blocker</activeProfile>
  </activeProfiles>
</settings>
EOF

# 检查是否创建成功
if [ -f "maven_settings.xml" ]; then
  echo "Maven 设置文件创建成功: maven_settings.xml"
  echo "使用以下命令构建项目:"
  echo "mvn clean package -DskipTests -Dmaven.wagon.http.ssl.insecure=true -Dmaven.wagon.http.ssl.allowall=true --settings maven_settings.xml"
else
  echo "Maven 设置文件创建失败"
fi 