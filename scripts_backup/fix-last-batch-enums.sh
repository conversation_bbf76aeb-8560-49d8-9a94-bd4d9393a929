#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

header() {
    echo -e "\n${BLUE}==== $1 ====${NC}\n"
}

header "修复最后一批枚举文件"

# 需要修复的剩余枚举文件
PROBLEM_FILES=(
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/resource/ResourceCategoryEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/CommonEntityStatusEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/KnowledgeFileStatusEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/team/RoleTypeEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/partner/PartnerTypeEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/common/SortOrderEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/ApplicationChatApplyStatusEnum.java"
)

FIXED_COUNT=0
FAILED_COUNT=0

for file in "${PROBLEM_FILES[@]}"; do
    if [ -f "$file" ]; then
        info "修复枚举文件: $file"
        # 创建备份
        cp "$file" "${file}.bak"
        
        # 获取包名
        package=$(grep -m 1 "package" "$file" | sed 's/package\s*\(.*\);/\1/')
        
        # 获取类名
        class_name=$(basename "$file" .java)
        
        # 根据文件名创建枚举模板
        case "$class_name" in
            "ResourceCategoryEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.resource;

/**
 * 资源分类枚举
 * 自动修复生成
 */
public enum ResourceCategoryEnum {
    IMAGE(0, "图片"),
    VIDEO(1, "视频"),
    DOCUMENT(2, "文档"),
    AUDIO(3, "音频");

    private final int code;
    private final String desc;

    ResourceCategoryEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ResourceCategoryEnum getByCode(int code) {
        for (ResourceCategoryEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "CommonEntityStatusEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 通用实体状态枚举
 * 自动修复生成
 */
public enum CommonEntityStatusEnum {
    ACTIVE(0, "激活"),
    INACTIVE(1, "未激活"),
    DELETED(2, "已删除");

    private final int code;
    private final String desc;

    CommonEntityStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CommonEntityStatusEnum getByCode(int code) {
        for (CommonEntityStatusEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "KnowledgeFileStatusEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 知识文件状态枚举
 * 自动修复生成
 */
public enum KnowledgeFileStatusEnum {
    PENDING(0, "待处理"),
    PROCESSING(1, "处理中"),
    COMPLETED(2, "已完成"),
    FAILED(3, "失败");

    private final int code;
    private final String desc;

    KnowledgeFileStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static KnowledgeFileStatusEnum getByCode(int code) {
        for (KnowledgeFileStatusEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "RoleTypeEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.team;

/**
 * 角色类型枚举
 * 自动修复生成
 */
public enum RoleTypeEnum {
    ADMIN(0, "管理员"),
    MEMBER(1, "成员"),
    GUEST(2, "访客");

    private final int code;
    private final String desc;

    RoleTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static RoleTypeEnum getByCode(int code) {
        for (RoleTypeEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "PartnerTypeEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.partner;

/**
 * 合作伙伴类型枚举
 * 自动修复生成
 */
public enum PartnerTypeEnum {
    INDIVIDUAL(0, "个人"),
    COMPANY(1, "公司"),
    ORGANIZATION(2, "组织");

    private final int code;
    private final String desc;

    PartnerTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static PartnerTypeEnum getByCode(int code) {
        for (PartnerTypeEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "SortOrderEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.common;

/**
 * 排序顺序枚举
 * 自动修复生成
 */
public enum SortOrderEnum {
    ASC(0, "升序"),
    DESC(1, "降序");

    private final int code;
    private final String desc;

    SortOrderEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static SortOrderEnum getByCode(int code) {
        for (SortOrderEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "ApplicationChatApplyStatusEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 应用聊天申请状态枚举
 * 自动修复生成
 */
public enum ApplicationChatApplyStatusEnum {
    PENDING(0, "待处理"),
    APPROVED(1, "已批准"),
    REJECTED(2, "已拒绝");

    private final int code;
    private final String desc;

    ApplicationChatApplyStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ApplicationChatApplyStatusEnum getByCode(int code) {
        for (ApplicationChatApplyStatusEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            *)
                warn "未知枚举类: $class_name"
                FAILED_COUNT=$((FAILED_COUNT + 1))
                continue
                ;;
        esac
        
        if [ $? -eq 0 ]; then
            FIXED_COUNT=$((FIXED_COUNT + 1))
            info "修复成功: $file"
        else
            FAILED_COUNT=$((FAILED_COUNT + 1))
            error "修复失败: $file"
        fi
    else
        warn "文件不存在: $file"
        FAILED_COUNT=$((FAILED_COUNT + 1))
    fi
done

header "修复总结"
info "成功修复: $FIXED_COUNT 个文件"
if [ $FAILED_COUNT -gt 0 ]; then
    warn "修复失败: $FAILED_COUNT 个文件"
fi

exit 0 