#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 枚举文件列表
ENUM_FILES=(
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/QwQaKnowledgeStatusEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/application/ApplicationAuthStatusEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/knowledge/KnowledgeAuthStatusEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/resource/ResourceDeleteStatusEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/practice/DetailDataEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/AppInteractionEnum.java"
)

info "开始修复更多枚举类结构问题..."

# 找出所有枚举类文件
ALL_ENUM_FILES=$(find chat-tag-system-api/src/main/java -type f -name "*.java" -exec grep -l "public enum" {} \;)

info "找到的所有枚举类文件:"
echo "$ALL_ENUM_FILES"

# 遍历所有枚举类文件
for enum_file in $ALL_ENUM_FILES; do
    info "处理文件: $enum_file"
    
    # 创建备份
    cp "$enum_file" "${enum_file}.bak"
    
    # 读取文件内容
    content=$(cat "$enum_file")
    
    # 提取包名
    package_line=$(echo "$content" | grep -E "^package")
    
    # 提取导入语句
    imports=$(echo "$content" | grep -E "^import")
    
    # 提取注解
    annotations=$(echo "$content" | grep -E "^@")
    
    # 提取枚举类名
    enum_declaration=$(echo "$content" | grep -E "public enum [A-Za-z0-9_]+")
    enum_name=$(echo "$enum_declaration" | sed -E 's/public enum ([A-Za-z0-9_]+).*/\1/')
    
    # 提取枚举常量 (通常是大写字母，后面跟着括号)
    constants=$(echo "$content" | grep -E "    [A-Z_]+\(.*\)" | grep -v "private")
    
    if [ -n "$constants" ]; then
        # 重新组织文件内容
        new_content="$package_line

$imports

$annotations
public enum $enum_name {
$constants
    
    private String code;
    private String desc;
    
    ${enum_name}(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    ${enum_name}(String code) {
        this.code = code;
        this.desc = code;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
}
"
        # 写入新内容
        echo "$new_content" > "$enum_file"
        info "已修复文件: $enum_file"
    else
        warn "无法提取枚举常量: $enum_file"
    fi
done

info "枚举类结构修复完成" 