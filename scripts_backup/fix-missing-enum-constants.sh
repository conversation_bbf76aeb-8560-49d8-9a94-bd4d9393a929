#!/bin/bash

# 查找枚举文件
find chat-tag-system-api/src/main/java -name "*.java" -type f -exec grep -l "public enum" {} \; | while read enum_file; do
    # 检查是否缺少枚举常量
    if ! grep -q "[A-Z_]*(.*)" "$enum_file"; then
        echo "修复文件: $enum_file"
        
        # 获取文件名（不含路径和扩展名）
        filename=$(basename "$enum_file" .java)
        
        # 检出原始文件
        git checkout "$enum_file"
        
        # 检查是否成功检出
        if [ $? -eq 0 ]; then
            echo "成功检出原始文件: $enum_file"
        else
            echo "检出文件失败: $enum_file"
            continue
        fi
    fi
done

echo "所有缺少枚举常量的枚举文件修复完成!" 