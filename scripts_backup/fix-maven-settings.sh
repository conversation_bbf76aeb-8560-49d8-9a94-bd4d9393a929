#!/bin/bash

# 创建正确的Maven设置文件
echo "创建Maven设置文件..."
MAVEN_SETTINGS_FILE="maven_settings.xml"

cat > $MAVEN_SETTINGS_FILE << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">

  <localRepository>${user.home}/.m2/repository</localRepository>

  <mirrors>
    <mirror>
      <id>lianjia-group-mirror</id>
      <mirrorOf>central</mirrorOf>
      <name>Lianjia Group Maven Repository</name>
      <url>http://maven.lianjia.com/content/groups/Lianjia-Group</url>
    </mirror>
  </mirrors>

  <profiles>
    <profile>
      <id>lianjia-profile</id>
      <repositories>
        <repository>
          <id>lianjia-group</id>
          <name>Lianjia Group Maven Repository</name>
          <url>http://maven.lianjia.com/content/groups/Lianjia-Group</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
        </repository>
      </repositories>
      <pluginRepositories>
        <pluginRepository>
          <id>lianjia-group</id>
          <name>Lianjia Group Maven Repository</name>
          <url>http://maven.lianjia.com/content/groups/Lianjia-Group</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
        </pluginRepository>
      </pluginRepositories>
    </profile>
    
    <profile>
      <id>disable-http-blocker</id>
      <properties>
        <maven.resolver.transport.http.blocker.disabled>true</maven.resolver.transport.http.blocker.disabled>
      </properties>
    </profile>
  </profiles>

  <activeProfiles>
    <activeProfile>lianjia-profile</activeProfile>
    <activeProfile>disable-http-blocker</activeProfile>
  </activeProfiles>

</settings>
EOF

echo "Maven设置文件已创建: $MAVEN_SETTINGS_FILE"
echo "使用以下命令构建项目:"
echo "mvn clean package -DskipTests -Dmaven.wagon.http.ssl.insecure=true -Dmaven.wagon.http.ssl.allowall=true -Dmaven.resolver.transport.http.blocker.disabled=true --settings $MAVEN_SETTINGS_FILE" 