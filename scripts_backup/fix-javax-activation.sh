#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

header() {
    echo -e "\n${BLUE}==== $1 ====${NC}\n"
}

header "修复javax.activation依赖问题"

# 检查pom.xml文件
API_POM_FILE="chat-tag-system-api/pom.xml"
if [ -f "$API_POM_FILE" ]; then
    info "找到API模块POM文件: $API_POM_FILE"
    
    # 检查是否已有javax.activation依赖
    if grep -q "javax.activation" "$API_POM_FILE"; then
        info "已存在javax.activation依赖，无需添加"
    else
        # 创建备份
        cp "$API_POM_FILE" "${API_POM_FILE}.bak"
        info "已创建备份文件: ${API_POM_FILE}.bak"
        
        # 添加javax.activation依赖
        info "添加javax.activation依赖..."
        
        # 查找dependencies标签位置
        DEPENDENCIES_END=$(grep -n "</dependencies>" "$API_POM_FILE" | head -1 | cut -d: -f1)
        
        if [ -n "$DEPENDENCIES_END" ]; then
            # 添加依赖
            sed -i.tmp "${DEPENDENCIES_END}i\\
        <!-- Java 9+ 兼容性所需依赖 -->\\\n        <dependency>\\\n            <groupId>javax.activation</groupId>\\\n            <artifactId>javax.activation-api</artifactId>\\\n            <version>1.2.0</version>\\\n        </dependency>" "$API_POM_FILE"
            rm -f "${API_POM_FILE}.tmp"
            
            info "已成功添加javax.activation依赖"
        else
            error "未找到dependencies标签，无法添加依赖"
        fi
    fi
else
    error "未找到API模块POM文件: $API_POM_FILE"
fi

# 检查是否需要添加Jakarta Mail依赖
if [ -f "$API_POM_FILE" ]; then
    if grep -q "S3StorageService" "$API_POM_FILE" || grep -q "MimeType" "$API_POM_FILE"; then
        info "检测到可能需要Jakarta Mail API依赖"
        
        if ! grep -q "jakarta.mail" "$API_POM_FILE" && ! grep -q "javax.mail" "$API_POM_FILE"; then
            # 添加Jakarta Mail依赖
            info "添加Jakarta Mail依赖..."
            
            # 查找dependencies标签位置
            DEPENDENCIES_END=$(grep -n "</dependencies>" "$API_POM_FILE" | head -1 | cut -d: -f1)
            
            if [ -n "$DEPENDENCIES_END" ]; then
                # 添加依赖
                sed -i.tmp "${DEPENDENCIES_END}i\\
        <!-- Jakarta Mail API 依赖 -->\\\n        <dependency>\\\n            <groupId>com.sun.mail</groupId>\\\n            <artifactId>jakarta.mail</artifactId>\\\n            <version>1.6.7</version>\\\n        </dependency>" "$API_POM_FILE"
                rm -f "${API_POM_FILE}.tmp"
                
                info "已成功添加Jakarta Mail依赖"
            else
                error "未找到dependencies标签，无法添加依赖"
            fi
        else
            info "已存在Mail相关依赖，无需添加"
        fi
    fi
fi

# 检查是否有其他缺失依赖
ROOT_POM_FILE="pom.xml"
if [ -f "$ROOT_POM_FILE" ]; then
    info "检查根POM文件中是否缺少依赖..."
    
    # 添加javax.xml.bind依赖（Java 11+所需）
    if ! grep -q "javax.xml.bind" "$ROOT_POM_FILE"; then
        info "添加javax.xml.bind依赖(Java 11+所需)..."
        
        # 查找dependencyManagement标签位置
        DEPENDENCY_MANAGEMENT=$(grep -n "<dependencyManagement>" "$ROOT_POM_FILE" | head -1 | cut -d: -f1)
        
        if [ -n "$DEPENDENCY_MANAGEMENT" ]; then
            # 查找dependencyManagement下的dependencies结束标签
            DEP_END=$(grep -n "</dependencies>" "$ROOT_POM_FILE" | head -1 | cut -d: -f1)
            
            if [ -n "$DEP_END" ]; then
                # 添加依赖
                sed -i.tmp "${DEP_END}i\\
            <!-- Java 11+ 兼容性所需依赖 -->\\\n            <dependency>\\\n                <groupId>javax.xml.bind</groupId>\\\n                <artifactId>jaxb-api</artifactId>\\\n                <version>2.3.1</version>\\\n            </dependency>" "$ROOT_POM_FILE"
                rm -f "${ROOT_POM_FILE}.tmp"
                
                info "已成功添加javax.xml.bind依赖"
            else
                error "未找到dependencyManagement/dependencies结束标签"
            fi
        else
            error "未找到dependencyManagement标签"
        fi
    else
        info "已存在javax.xml.bind依赖，无需添加"
    fi
fi

header "修复完成"
info "已添加javax.activation和其他兼容性依赖"
info "现在您可以尝试重新构建项目:"
echo "JAVA_HOME=\"/Library/Java/JavaVirtualMachines/adoptopenjdk-11.jdk/Contents/Home\" mvn -t toolchains.xml clean package -DskipTests -Dmaven.resolver.transport.http.blocker.disabled=true -Dmaven.wagon.http.ssl.insecure=true -DremoteRepositories=http://maven.lianjia.com/content/groups/Lianjia-Group -U" 