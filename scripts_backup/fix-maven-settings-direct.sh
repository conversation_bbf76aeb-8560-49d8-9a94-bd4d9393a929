#!/bin/bash

# 直接修改Maven设置文件的脚本
# 彻底解决Maven仓库HTTP阻止问题

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取用户Maven设置文件位置
USER_HOME="$HOME"
MAVEN_SETTINGS_FILE="$USER_HOME/.m2/settings.xml"
MAVEN_SETTINGS_BACKUP="$USER_HOME/.m2/settings.xml.backup.$(date +%Y%m%d%H%M%S)"

# 首先备份现有文件（如果存在）
if [ -f "$MAVEN_SETTINGS_FILE" ]; then
    info "备份现有Maven设置文件到 $MAVEN_SETTINGS_BACKUP"
    cp "$MAVEN_SETTINGS_FILE" "$MAVEN_SETTINGS_BACKUP"
else
    info "未找到现有Maven设置文件，将创建新文件"
    # 确保.m2目录存在
    mkdir -p "$USER_HOME/.m2"
fi

# 创建新的设置文件
info "创建新的Maven设置文件..."

cat > "$MAVEN_SETTINGS_FILE" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">

  <localRepository>${user.home}/.m2/repository</localRepository>

  <mirrors>
    <!-- 允许HTTP协议的链家/贝壳内部仓库镜像 -->
    <mirror>
      <id>lianjia-group-mirror</id>
      <mirrorOf>*</mirrorOf>
      <name>Lianjia Group Repository</name>
      <url>http://maven.lianjia.com/content/groups/Lianjia-Group</url>
    </mirror>
  </mirrors>

  <profiles>
    <!-- 贝壳/链家仓库配置 -->
    <profile>
      <id>lianjia-profile</id>
      <repositories>
        <repository>
          <id>lianjia-group</id>
          <name>Lianjia Group Repository</name>
          <url>http://maven.lianjia.com/content/groups/Lianjia-Group</url>
          <releases><enabled>true</enabled></releases>
          <snapshots><enabled>true</enabled></snapshots>
        </repository>
      </repositories>
      <pluginRepositories>
        <pluginRepository>
          <id>lianjia-group</id>
          <name>Lianjia Group Repository</name>
          <url>http://maven.lianjia.com/content/groups/Lianjia-Group</url>
          <releases><enabled>true</enabled></releases>
          <snapshots><enabled>true</enabled></snapshots>
        </pluginRepository>
      </pluginRepositories>
    </profile>
    
    <!-- 禁用HTTP阻止器 -->
    <profile>
      <id>disable-http-blocker</id>
      <properties>
        <maven.resolver.transport.http.blocker.disabled>true</maven.resolver.transport.http.blocker.disabled>
        <maven.wagon.http.ssl.insecure>true</maven.wagon.http.ssl.insecure>
        <maven.wagon.http.ssl.allowall>true</maven.wagon.http.ssl.allowall>
      </properties>
    </profile>
  </profiles>

  <activeProfiles>
    <activeProfile>lianjia-profile</activeProfile>
    <activeProfile>disable-http-blocker</activeProfile>
  </activeProfiles>

  <!-- 关闭镜像的HTTP阻止 -->
  <servers>
    <server>
      <id>lianjia-group-mirror</id>
      <configuration>
        <httpConfiguration>
          <all>
            <usePreemptive>true</usePreemptive>
            <params>
              <property>
                <name>http.protocol.allow-circular-redirects</name>
                <value>true</value>
              </property>
            </params>
          </all>
        </httpConfiguration>
      </configuration>
    </server>
  </servers>
</settings>
EOF

if [ $? -eq 0 ]; then
    info "Maven设置文件已成功创建: $MAVEN_SETTINGS_FILE"
    # 为了确保权限正确
    chmod 644 "$MAVEN_SETTINGS_FILE"
    
    # 检查Maven环境
    if command -v mvn &> /dev/null; then
        info "Maven已安装，测试配置..."
        # 清除Maven本地缓存（可选）
        read -p "是否清除Maven本地缓存？这可能解决一些依赖问题（y/n）: " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            info "清除Maven本地缓存..."
            rm -rf "$USER_HOME/.m2/repository/com/lianjia"
            info "缓存已清除"
        fi

        # 显示命令提示
        info "现在可以使用以下命令构建项目:"
        echo "mvn clean package -DskipTests"
        
        # 询问是否立即构建
        read -p "是否立即构建项目？(y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            info "开始构建项目..."
            cd "$(dirname "$0")"  # 确保在项目根目录
            mvn clean package -DskipTests
            if [ $? -eq 0 ]; then
                info "构建成功！"
            else
                error "构建失败，请检查错误信息"
            fi
        fi
    else
        warn "未检测到Maven，请安装Maven后再试"
    fi
else
    error "创建Maven设置文件失败"
fi

info "如需恢复原始设置，请执行: cp $MAVEN_SETTINGS_BACKUP $MAVEN_SETTINGS_FILE" 