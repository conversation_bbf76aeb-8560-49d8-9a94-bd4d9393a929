#!/bin/bash

# 创建日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

log "开始修复工具类和DTO类..."

# 修复特定文件
fix_sensitive_match_response_dto() {
    log "修复 SensitiveMatchResponseDTO 类..."
    file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/dto/conversation/SensitiveMatchResponseDTO.java"
    
    if [ -f "$file" ]; then
        log "修改 $file"
        # 检查是否已经有 Lombok 注解
        if ! grep -q "@Data" "$file"; then
            # 添加 Lombok 注解
            sed -i '' '/^package/a\
import lombok.Data;\
' "$file"
            
            # 添加 @Data 注解到类
            sed -i '' '/^public class/i\
@Data' "$file"
            
            log "已添加 Lombok 注解到 SensitiveMatchResponseDTO 类"
        fi
        
        # 修复 SensitiveMatchInfoDTO 类
        info_dto_file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/dto/sensitive/SensitiveMatchInfoDTO.java"
        if [ -f "$info_dto_file" ]; then
            log "修改 $info_dto_file"
            # 检查是否已经有 Lombok 注解
            if ! grep -q "@Data" "$info_dto_file"; then
                # 添加 Lombok 注解
                sed -i '' '/^package/a\
import lombok.Data;\
' "$info_dto_file"
                
                # 添加 @Data 注解到类
                sed -i '' '/^public class/i\
@Data' "$info_dto_file"
                
                log "已添加 Lombok 注解到 SensitiveMatchInfoDTO 类"
            fi
            
            # 添加数据字段和方法
            if ! grep -q "private DataDetail data;" "$info_dto_file"; then
                sed -i '' '/^public class/a\
    private DataDetail data;\
\
    public DataDetail getData() {\
        return data;\
    }\
\
    public void setData(DataDetail data) {\
        this.data = data;\
    }\
\
    @Data\
    public static class DataDetail {\
        private String word;\
\
        public String getWord() {\
            return word;\
        }\
\
        public void setWord(String word) {\
            this.word = word;\
        }\
    }' "$info_dto_file"
                
                log "已添加 DataDetail 字段和方法到 SensitiveMatchInfoDTO 类"
            fi
        fi
    fi
}

# 修复 RagResp 类
fix_rag_resp() {
    log "修复 RagResp 类..."
    file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/dto/rag/RagResp.java"
    
    if [ -f "$file" ]; then
        log "修改 $file"
        # 检查是否已经有 Lombok 注解
        if ! grep -q "@Data" "$file"; then
            # 添加 Lombok 注解
            sed -i '' '/^package/a\
import lombok.Data;\
' "$file"
            
            # 添加 @Data 注解到类
            sed -i '' '/^public class/i\
@Data' "$file"
            
            log "已添加 Lombok 注解到 RagResp 类"
        fi
        
        # 添加 status 字段和方法
        if ! grep -q "private String status;" "$file"; then
            sed -i '' '/^public class/a\
    private String status;\
\
    public String getStatus() {\
        return status;\
    }\
\
    public void setStatus(String status) {\
        this.status = status;\
    }' "$file"
            
            log "已添加 status 字段和方法到 RagResp 类"
        fi
    fi
}

# 修复 RoleDTO 类
fix_role_dto() {
    log "修复 RoleDTO 类..."
    file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/dto/home/<USER>"
    
    if [ -f "$file" ]; then
        log "修改 $file"
        # 检查是否已经有 Lombok 注解
        if ! grep -q "@Data" "$file"; then
            # 添加 Lombok 注解
            sed -i '' '/^package/a\
import lombok.Data;\
' "$file"
            
            # 添加 @Data 注解到类
            sed -i '' '/^public class/i\
@Data' "$file"
            
            log "已添加 Lombok 注解到 RoleDTO 类"
        fi
        
        # 添加 Role 内部类的 @Data 注解和 icon 字段
        if grep -q "public static class Role" "$file" && ! grep -q "@Data.*public static class Role" "$file"; then
            sed -i '' 's/public static class Role/\@Data\
    public static class Role/g' "$file"
            
            # 检查是否有 icon 字段
            if ! grep -q "private String icon;" "$file"; then
                sed -i '' '/public static class Role/a\
        private String icon;\
\
        public String getIcon() {\
            return icon;\
        }\
\
        public void setIcon(String icon) {\
            this.icon = icon;\
        }' "$file"
                
                log "已添加 icon 字段和方法到 RoleDTO.Role 类"
            fi
            
            log "已添加 @Data 注解到 RoleDTO.Role 类"
        fi
    fi
}

# 修复 KnowledgeDTO 类
fix_knowledge_dto() {
    log "修复 KnowledgeDTO 类..."
    file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/dto/home/<USER>"
    
    if [ -f "$file" ]; then
        log "修改 $file"
        # 检查是否已经有 Lombok 注解
        if ! grep -q "@Data" "$file"; then
            # 添加 Lombok 注解
            sed -i '' '/^package/a\
import lombok.Data;\
' "$file"
            
            # 添加 @Data 注解到类
            sed -i '' '/^public class/i\
@Data' "$file"
            
            log "已添加 Lombok 注解到 KnowledgeDTO 类"
        fi
        
        # 添加 Assistant 内部类的 @Data 注解和 icon 字段
        if grep -q "public static class Assistant" "$file" && ! grep -q "@Data.*public static class Assistant" "$file"; then
            sed -i '' 's/public static class Assistant/\@Data\
    public static class Assistant/g' "$file"
            
            # 检查是否有 icon 字段
            if ! grep -q "private String icon;" "$file"; then
                sed -i '' '/public static class Assistant/a\
        private String icon;\
\
        public String getIcon() {\
            return icon;\
        }\
\
        public void setIcon(String icon) {\
            this.icon = icon;\
        }' "$file"
                
                log "已添加 icon 字段和方法到 KnowledgeDTO.Assistant 类"
            fi
            
            log "已添加 @Data 注解到 KnowledgeDTO.Assistant 类"
        fi
    fi
}

# 修复 ChatEvaluation 类
fix_chat_evaluation() {
    log "修复 ChatEvaluation 类..."
    file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/dto/evaluation/ChatEvaluation.java"
    
    if [ -f "$file" ]; then
        log "修改 $file"
        # 检查是否已经有 Lombok 注解
        if ! grep -q "@Data" "$file"; then
            # 添加 Lombok 注解
            sed -i '' '/^package/a\
import lombok.Data;\
' "$file"
            
            # 添加 @Data 注解到类
            sed -i '' '/^public class/i\
@Data' "$file"
            
            log "已添加 Lombok 注解到 ChatEvaluation 类"
        fi
        
        # 添加 Evaluation 内部类的 @Data 注解
        if grep -q "public static class Evaluation" "$file" && ! grep -q "@Data.*public static class Evaluation" "$file"; then
            sed -i '' 's/public static class Evaluation/\@Data\
    public static class Evaluation/g' "$file"
            
            # 添加必要的字段和方法
            if ! grep -q "private String result;" "$file"; then
                sed -i '' '/public static class Evaluation/a\
        private String result;\
        private List<EvaluationReason> reason;\
\
        public String getResult() {\
            return result;\
        }\
\
        public void setResult(String result) {\
            this.result = result;\
        }\
\
        public List<EvaluationReason> getReason() {\
            return reason;\
        }\
\
        public void setReason(List<EvaluationReason> reason) {\
            this.reason = reason;\
        }' "$file"
                
                log "已添加字段和方法到 ChatEvaluation.Evaluation 类"
            fi
            
            log "已添加 @Data 注解到 ChatEvaluation.Evaluation 类"
        fi
        
        # 添加 EvaluationReason 内部类的 @Data 注解
        if grep -q "public static class EvaluationReason" "$file" && ! grep -q "@Data.*public static class EvaluationReason" "$file"; then
            sed -i '' 's/public static class EvaluationReason/\@Data\
    public static class EvaluationReason/g' "$file"
            
            # 添加必要的字段和方法
            if ! grep -q "private String name;" "$file" || ! grep -q "private String feedback;" "$file"; then
                sed -i '' '/public static class EvaluationReason/a\
        private String name;\
        private String feedback;\
\
        public String getName() {\
            return name;\
        }\
\
        public void setName(String name) {\
            this.name = name;\
        }\
\
        public String getFeedback() {\
            return feedback;\
        }\
\
        public void setFeedback(String feedback) {\
            this.feedback = feedback;\
        }' "$file"
                
                log "已添加字段和方法到 ChatEvaluation.EvaluationReason 类"
            fi
            
            log "已添加 @Data 注解到 ChatEvaluation.EvaluationReason 类"
        fi
    fi
}

# 修复 Tool 相关类
fix_tool_classes() {
    log "修复 Tool 相关类..."
    
    # 修复 Tool 类
    tool_file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/dto/assistant/tool/Tool.java"
    if [ -f "$tool_file" ]; then
        log "修改 $tool_file"
        # 检查是否已经有 Lombok 注解
        if ! grep -q "@Data" "$tool_file"; then
            # 添加 Lombok 注解
            sed -i '' '/^package/a\
import lombok.Data;\
import lombok.NoArgsConstructor;\
import lombok.AllArgsConstructor;\
' "$tool_file"
            
            # 添加 @Data 注解到类
            sed -i '' '/^public class/i\
@Data\
@NoArgsConstructor\
@AllArgsConstructor' "$tool_file"
            
            log "已添加 Lombok 注解到 Tool 类"
        fi
        
        # 添加必要的字段和方法
        if ! grep -q "private Function function;" "$tool_file"; then
            sed -i '' '/^public class/a\
    private String type = "function";\
    private Function function;\
\
    public Tool(String type) {\
        this.type = type;\
    }\
\
    public Function getFunction() {\
        return function;\
    }\
\
    public void setFunction(Function function) {\
        this.function = function;\
    }' "$tool_file"
            
            log "已添加字段和方法到 Tool 类"
        fi
    fi
    
    # 修复 Function 类
    function_file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/dto/assistant/tool/Function.java"
    if [ -f "$function_file" ]; then
        log "修改 $function_file"
        # 检查是否已经有 Lombok 注解
        if ! grep -q "@Data" "$function_file"; then
            # 添加 Lombok 注解
            sed -i '' '/^package/a\
import lombok.Data;\
' "$function_file"
            
            # 添加 @Data 注解到类
            sed -i '' '/^public class/i\
@Data' "$function_file"
            
            log "已添加 Lombok 注解到 Function 类"
        fi
        
        # 添加必要的字段和方法
        if ! grep -q "private String name;" "$function_file"; then
            sed -i '' '/^public class/a\
    private String name;\
\
    public String getName() {\
        return name;\
    }\
\
    public void setName(String name) {\
        this.name = name;\
    }' "$function_file"
            
            log "已添加字段和方法到 Function 类"
        fi
    fi
    
    # 修复 AuthData 类
    auth_data_file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/dto/assistant/tool/AuthData.java"
    if [ -f "$auth_data_file" ]; then
        log "修改 $auth_data_file"
        # 检查是否已经有 Lombok 注解
        if ! grep -q "@Data" "$auth_data_file"; then
            # 添加 Lombok 注解
            sed -i '' '/^package/a\
import lombok.Data;\
' "$auth_data_file"
            
            # 添加 @Data 注解到类
            sed -i '' '/^public class/i\
@Data' "$auth_data_file"
            
            log "已添加 Lombok 注解到 AuthData 类"
        fi
        
        # 添加必要的字段和方法
        if ! grep -q "private String addTo;" "$auth_data_file"; then
            sed -i '' '/^public class/a\
    private String addTo;\
    private List<Param> params;\
\
    public String getAddTo() {\
        return addTo;\
    }\
\
    public void setAddTo(String addTo) {\
        this.addTo = addTo;\
    }\
\
    public List<Param> getParams() {\
        return params;\
    }\
\
    public void setParams(List<Param> params) {\
        this.params = params;\
    }' "$auth_data_file"
            
            log "已添加字段和方法到 AuthData 类"
        fi
        
        # 添加 Param 内部类的 @Data 注解
        if grep -q "public static class Param" "$auth_data_file" && ! grep -q "@Data.*public static class Param" "$auth_data_file"; then
            sed -i '' 's/public static class Param/\@Data\
    public static class Param/g' "$auth_data_file"
            
            # 添加必要的字段和方法
            if ! grep -q "private String value;" "$auth_data_file"; then
                sed -i '' '/public static class Param/a\
        private String value;\
\
        public String getValue() {\
            return value;\
        }\
\
        public void setValue(String value) {\
            this.value = value;\
        }' "$auth_data_file"
                
                log "已添加字段和方法到 AuthData.Param 类"
            fi
            
            log "已添加 @Data 注解到 AuthData.Param 类"
        fi
    fi
    
    # 修复 ToolSchema 类
    tool_schema_file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/dto/assistant/tool/ToolSchema.java"
    if [ -f "$tool_schema_file" ]; then
        log "修改 $tool_schema_file"
        # 检查是否已经有 Lombok 注解
        if ! grep -q "@Data" "$tool_schema_file"; then
            # 添加 Lombok 注解
            sed -i '' '/^package/a\
import lombok.Data;\
' "$tool_schema_file"
            
            # 添加 @Data 注解到类
            sed -i '' '/^public class/i\
@Data' "$tool_schema_file"
            
            log "已添加 Lombok 注解到 ToolSchema 类"
        fi
        
        # 添加必要的字段和方法
        if ! grep -q "private List<Server> servers;" "$tool_schema_file"; then
            sed -i '' '/^public class/a\
    private List<Server> servers;\
\
    public List<Server> getServers() {\
        return servers;\
    }\
\
    public void setServers(List<Server> servers) {\
        this.servers = servers;\
    }' "$tool_schema_file"
            
            log "已添加字段和方法到 ToolSchema 类"
        fi
        
        # 添加 Server 内部类的 @Data 注解
        if grep -q "public static class Server" "$tool_schema_file" && ! grep -q "@Data.*public static class Server" "$tool_schema_file"; then
            sed -i '' 's/public static class Server/\@Data\
    public static class Server/g' "$tool_schema_file"
            
            # 添加必要的字段和方法
            if ! grep -q "private String url;" "$tool_schema_file"; then
                sed -i '' '/public static class Server/a\
        private String url;\
\
        public String getUrl() {\
            return url;\
        }\
\
        public void setUrl(String url) {\
            this.url = url;\
        }' "$tool_schema_file"
                
                log "已添加字段和方法到 ToolSchema.Server 类"
            fi
            
            log "已添加 @Data 注解到 ToolSchema.Server 类"
        fi
    fi
}

# 修复 RolePermVO 类
fix_role_perm_vo() {
    log "修复 RolePermVO 类..."
    file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/vo/RolePermVO.java"
    
    if [ -f "$file" ]; then
        log "修改 $file"
        # 检查是否已经有 Lombok 注解
        if ! grep -q "@Data" "$file"; then
            # 添加 Lombok 注解
            sed -i '' '/^package/a\
import lombok.Data;\
' "$file"
            
            # 添加 @Data 注解到类
            sed -i '' '/^public class/i\
@Data' "$file"
            
            log "已添加 Lombok 注解到 RolePermVO 类"
        fi
        
        # 添加 PageChatPermsVO 内部类的 @Data 注解
        if grep -q "public static class PageChatPermsVO" "$file" && ! grep -q "@Data.*public static class PageChatPermsVO" "$file"; then
            sed -i '' 's/public static class PageChatPermsVO/\@Data\
    public static class PageChatPermsVO/g' "$file"
            
            # 添加 setChatChannels 方法
            if ! grep -q "public void setChatChannels" "$file"; then
                sed -i '' '/public static class PageChatPermsVO/a\
        private List<LabelVO> chatChannels;\
\
        public List<LabelVO> getChatChannels() {\
            return chatChannels;\
        }\
\
        public void setChatChannels(List<LabelVO> chatChannels) {\
            this.chatChannels = chatChannels;\
        }' "$file"
                
                log "已添加 chatChannels 字段和方法到 RolePermVO.PageChatPermsVO 类"
            fi
            
            log "已添加 @Data 注解到 RolePermVO.PageChatPermsVO 类"
        fi
    fi
}

# 修复 InteractionSaveReq 类
fix_interaction_save_req() {
    log "修复 InteractionSaveReq 类..."
    file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/vo/practice/InteractionSaveReq.java"
    
    if [ -f "$file" ]; then
        log "修改 $file"
        # 检查是否已经有 Lombok 注解
        if ! grep -q "@Data" "$file"; then
            # 添加 Lombok 注解
            sed -i '' '/^package/a\
import lombok.Data;\
' "$file"
            
            # 添加 @Data 注解到类
            sed -i '' '/^public class/i\
@Data' "$file"
            
            log "已添加 Lombok 注解到 InteractionSaveReq 类"
        fi
        
        # 添加必要的字段和方法
        if ! grep -q "private String taskType;" "$file"; then
            sed -i '' '/^public class/a\
    private String taskType;\
    private String taskId;\
    private String recordType;\
\
    public String getTaskType() {\
        return taskType;\
    }\
\
    public void setTaskType(String taskType) {\
        this.taskType = taskType;\
    }\
\
    public String getTaskId() {\
        return taskId;\
    }\
\
    public void setTaskId(String taskId) {\
        this.taskId = taskId;\
    }\
\
    public String getRecordType() {\
        return recordType;\
    }\
\
    public void setRecordType(String recordType) {\
        this.recordType = recordType;\
    }' "$file"
            
            log "已添加字段和方法到 InteractionSaveReq 类"
        fi
    fi
}

# 修复日志记录器
fix_loggers() {
    log "修复缺少的日志记录器..."
    
    # 修复 HttpResponseUtil 类
    http_response_util_file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/util/HttpResponseUtil.java"
    if [ -f "$http_response_util_file" ]; then
        log "修改 $http_response_util_file"
        
        # 检查是否已导入 Logger
        if ! grep -q "import org.slf4j.Logger" "$http_response_util_file"; then
            sed -i '' '/^package/a\
import org.slf4j.Logger;\
import org.slf4j.LoggerFactory;\
' "$http_response_util_file"
            
            log "已添加 Logger 导入到 HttpResponseUtil 类"
        fi
        
        # 添加 LOGGER 字段
        if ! grep -q "private static final Logger LOGGER" "$http_response_util_file"; then
            sed -i '' '/^public class/a\
    private static final Logger LOGGER = LoggerFactory.getLogger(HttpResponseUtil.class);' "$http_response_util_file"
            
            log "已添加 LOGGER 字段到 HttpResponseUtil 类"
        fi
    fi
    
    # 修复 JsoupUtil 类
    jsoup_util_file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/util/JsoupUtil.java"
    if [ -f "$jsoup_util_file" ]; then
        log "修改 $jsoup_util_file"
        
        # 检查是否已导入 Logger
        if ! grep -q "import org.slf4j.Logger" "$jsoup_util_file"; then
            sed -i '' '/^package/a\
import org.slf4j.Logger;\
import org.slf4j.LoggerFactory;\
' "$jsoup_util_file"
            
            log "已添加 Logger 导入到 JsoupUtil 类"
        fi
        
        # 添加 LOGGER 字段
        if ! grep -q "private static final Logger LOGGER" "$jsoup_util_file"; then
            sed -i '' '/^public class/a\
    private static final Logger LOGGER = LoggerFactory.getLogger(JsoupUtil.class);' "$jsoup_util_file"
            
            log "已添加 LOGGER 字段到 JsoupUtil 类"
        fi
    fi
    
    # 修复 ValidateWorkflowAuthorization 类
    validate_workflow_file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/validator/ValidateWorkflowAuthorization.java"
    if [ -f "$validate_workflow_file" ]; then
        log "修改 $validate_workflow_file"
        
        # 检查是否已导入 Logger
        if ! grep -q "import org.slf4j.Logger" "$validate_workflow_file"; then
            sed -i '' '/^package/a\
import org.slf4j.Logger;\
import org.slf4j.LoggerFactory;\
' "$validate_workflow_file"
            
            log "已添加 Logger 导入到 ValidateWorkflowAuthorization 类"
        fi
        
        # 添加 LOGGER 字段
        if ! grep -q "private static final Logger LOGGER" "$validate_workflow_file"; then
            sed -i '' '/^public class/a\
    private static final Logger LOGGER = LoggerFactory.getLogger(ValidateWorkflowAuthorization.class);' "$validate_workflow_file"
            
            log "已添加 LOGGER 字段到 ValidateWorkflowAuthorization 类"
        fi
    fi
    
    # 修复 CustomDateDeserializer 类
    custom_date_deserializer_file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/vo/partner/CustomDateDeserializer.java"
    if [ -f "$custom_date_deserializer_file" ]; then
        log "修改 $custom_date_deserializer_file"
        
        # 检查是否已导入 Logger
        if ! grep -q "import org.slf4j.Logger" "$custom_date_deserializer_file"; then
            sed -i '' '/^package/a\
import org.slf4j.Logger;\
import org.slf4j.LoggerFactory;\
' "$custom_date_deserializer_file"
            
            log "已添加 Logger 导入到 CustomDateDeserializer 类"
        fi
        
        # 添加 LOGGER 字段
        if ! grep -q "private static final Logger LOGGER" "$custom_date_deserializer_file"; then
            sed -i '' '/^public class/a\
    private static final Logger LOGGER = LoggerFactory.getLogger(CustomDateDeserializer.class);' "$custom_date_deserializer_file"
            
            log "已添加 LOGGER 字段到 CustomDateDeserializer 类"
        fi
    fi
}

# 执行修复
fix_sensitive_match_response_dto
fix_rag_resp
fix_role_dto
fix_knowledge_dto
fix_chat_evaluation
fix_tool_classes
fix_role_perm_vo
fix_interaction_save_req
fix_loggers

log "工具类和DTO类修复完成" 