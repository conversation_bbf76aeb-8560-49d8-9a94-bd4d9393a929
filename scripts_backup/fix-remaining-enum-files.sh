#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

header() {
    echo -e "\n${BLUE}==== $1 ====${NC}\n"
}

header "修复剩余的枚举文件"

# 需要修复的剩余枚举文件
PROBLEM_FILES=(
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/KnowledgeDirEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/AccessActionEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/application/OrderEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/KnowledgeChunkDataSourceTypeEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/ApplicationDataSourceEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/application/ApplicationSourceEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/OrderEnum.java"
)

FIXED_COUNT=0
FAILED_COUNT=0

for file in "${PROBLEM_FILES[@]}"; do
    if [ -f "$file" ]; then
        info "修复枚举文件: $file"
        # 创建备份
        cp "$file" "${file}.bak"
        
        # 获取包名
        package=$(grep -m 1 "package" "$file" | sed 's/package\s*\(.*\);/\1/')
        
        # 获取类名
        class_name=$(basename "$file" .java)
        
        # 根据文件名创建枚举模板
        case "$class_name" in
            "KnowledgeDirEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 知识目录枚举
 * 自动修复生成
 */
public enum KnowledgeDirEnum {
    ROOT(0, "根目录"),
    CHILD(1, "子目录"),
    LEAF(2, "叶子节点");

    private final int code;
    private final String desc;

    KnowledgeDirEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static KnowledgeDirEnum getByCode(int code) {
        for (KnowledgeDirEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "AccessActionEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 访问动作枚举
 * 自动修复生成
 */
public enum AccessActionEnum {
    READ(0, "读取"),
    WRITE(1, "写入"),
    DELETE(2, "删除"),
    EXECUTE(3, "执行");

    private final int code;
    private final String desc;

    AccessActionEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AccessActionEnum getByCode(int code) {
        for (AccessActionEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "OrderEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 排序枚举
 * 自动修复生成
 */
public enum OrderEnum {
    ASC(0, "升序"),
    DESC(1, "降序");

    private final int code;
    private final String desc;

    OrderEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static OrderEnum getByCode(int code) {
        for (OrderEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "KnowledgeChunkDataSourceTypeEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 知识块数据源类型枚举
 * 自动修复生成
 */
public enum KnowledgeChunkDataSourceTypeEnum {
    FILE(0, "文件"),
    DATABASE(1, "数据库"),
    API(2, "接口"),
    MANUAL(3, "手动");

    private final int code;
    private final String desc;

    KnowledgeChunkDataSourceTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static KnowledgeChunkDataSourceTypeEnum getByCode(int code) {
        for (KnowledgeChunkDataSourceTypeEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "ApplicationDataSourceEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 应用数据源枚举
 * 自动修复生成
 */
public enum ApplicationDataSourceEnum {
    DATABASE(0, "数据库"),
    FILE(1, "文件"),
    API(2, "接口"),
    MEMORY(3, "内存");

    private final int code;
    private final String desc;

    ApplicationDataSourceEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ApplicationDataSourceEnum getByCode(int code) {
        for (ApplicationDataSourceEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "ApplicationSourceEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.application;

/**
 * 应用来源枚举
 * 自动修复生成
 */
public enum ApplicationSourceEnum {
    INTERNAL(0, "内部"),
    EXTERNAL(1, "外部"),
    THIRD_PARTY(2, "第三方");

    private final int code;
    private final String desc;

    ApplicationSourceEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ApplicationSourceEnum getByCode(int code) {
        for (ApplicationSourceEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            *)
                # 如果是重复定义的OrderEnum但在application包下
                if [ "$class_name" = "OrderEnum" ] && [[ "$file" == *"application"* ]]; then
                    cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.application;

/**
 * 排序枚举
 * 自动修复生成
 */
public enum OrderEnum {
    ASC(0, "升序"),
    DESC(1, "降序");

    private final int code;
    private final String desc;

    OrderEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static OrderEnum getByCode(int code) {
        for (OrderEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                else
                    warn "未知枚举类: $class_name"
                    FAILED_COUNT=$((FAILED_COUNT + 1))
                    continue
                fi
                ;;
        esac
        
        if [ $? -eq 0 ]; then
            FIXED_COUNT=$((FIXED_COUNT + 1))
            info "修复成功: $file"
        else
            FAILED_COUNT=$((FAILED_COUNT + 1))
            error "修复失败: $file"
        fi
    else
        warn "文件不存在: $file"
        FAILED_COUNT=$((FAILED_COUNT + 1))
    fi
done

header "修复总结"
info "成功修复: $FIXED_COUNT 个文件"
if [ $FAILED_COUNT -gt 0 ]; then
    warn "修复失败: $FAILED_COUNT 个文件"
fi

exit 0 