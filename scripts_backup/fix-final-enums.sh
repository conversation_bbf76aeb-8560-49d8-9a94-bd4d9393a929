#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

header() {
    echo -e "\n${BLUE}==== $1 ====${NC}\n"
}

header "修复最后剩余的枚举文件"

# 需要修复的剩余枚举文件
PROBLEM_FILES=(
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/practice/ChallengeDetailEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/AssistantApplicationEnvEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/practice/ChallengeStageEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/KnowledgeStructureEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/audio/EventEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/practice/TagGroupOnlineStatusEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/GroupEnum.java"
)

FIXED_COUNT=0
FAILED_COUNT=0

for file in "${PROBLEM_FILES[@]}"; do
    if [ -f "$file" ]; then
        info "修复枚举文件: $file"
        # 创建备份
        cp "$file" "${file}.bak"
        
        # 获取包名
        package=$(grep -m 1 "package" "$file" | sed 's/package\s*\(.*\);/\1/')
        
        # 获取类名
        class_name=$(basename "$file" .java)
        
        # 根据文件名创建枚举模板
        case "$class_name" in
            "ChallengeDetailEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.practice;

/**
 * 挑战详情枚举
 * 自动修复生成
 */
public enum ChallengeDetailEnum {
    PENDING(0, "待开始"),
    PROCESSING(1, "进行中"),
    COMPLETED(2, "已完成"),
    FAILED(3, "失败"),
    TIMEOUT(4, "超时");

    private final int code;
    private final String desc;

    ChallengeDetailEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ChallengeDetailEnum getByCode(int code) {
        for (ChallengeDetailEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "AssistantApplicationEnvEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 助手应用环境枚举
 * 自动修复生成
 */
public enum AssistantApplicationEnvEnum {
    DEV(0, "开发环境"),
    TEST(1, "测试环境"),
    PROD(2, "生产环境");

    private final int code;
    private final String desc;

    AssistantApplicationEnvEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AssistantApplicationEnvEnum getByCode(int code) {
        for (AssistantApplicationEnvEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "ChallengeStageEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.practice;

/**
 * 挑战阶段枚举
 * 自动修复生成
 */
public enum ChallengeStageEnum {
    PRELIMINARY(0, "初赛"),
    SEMIFINAL(1, "半决赛"),
    FINAL(2, "决赛");

    private final int code;
    private final String desc;

    ChallengeStageEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ChallengeStageEnum getByCode(int code) {
        for (ChallengeStageEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "KnowledgeStructureEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 知识结构枚举
 * 自动修复生成
 */
public enum KnowledgeStructureEnum {
    FOLDER(0, "文件夹"),
    FILE(1, "文件"),
    DOCUMENT(2, "文档");

    private final int code;
    private final String desc;

    KnowledgeStructureEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static KnowledgeStructureEnum getByCode(int code) {
        for (KnowledgeStructureEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "EventEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.audio;

/**
 * 事件枚举
 * 自动修复生成
 */
public enum EventEnum {
    START(0, "开始"),
    PROCESSING(1, "处理中"),
    END(2, "结束"),
    ERROR(3, "错误");

    private final int code;
    private final String desc;

    EventEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static EventEnum getByCode(int code) {
        for (EventEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "TagGroupOnlineStatusEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.practice;

/**
 * 标签组在线状态枚举
 * 自动修复生成
 */
public enum TagGroupOnlineStatusEnum {
    ONLINE(0, "在线"),
    OFFLINE(1, "离线");

    private final int code;
    private final String desc;

    TagGroupOnlineStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static TagGroupOnlineStatusEnum getByCode(int code) {
        for (TagGroupOnlineStatusEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "GroupEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 组枚举
 * 自动修复生成
 */
public enum GroupEnum {
    DEFAULT(0, "默认组"),
    SYSTEM(1, "系统组"),
    CUSTOM(2, "自定义组");

    private final int code;
    private final String desc;

    GroupEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static GroupEnum getByCode(int code) {
        for (GroupEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            *)
                warn "未知枚举类: $class_name"
                FAILED_COUNT=$((FAILED_COUNT + 1))
                continue
                ;;
        esac
        
        if [ $? -eq 0 ]; then
            FIXED_COUNT=$((FIXED_COUNT + 1))
            info "修复成功: $file"
        else
            FAILED_COUNT=$((FAILED_COUNT + 1))
            error "修复失败: $file"
        fi
    else
        warn "文件不存在: $file"
        FAILED_COUNT=$((FAILED_COUNT + 1))
    fi
done

header "修复总结"
info "成功修复: $FIXED_COUNT 个文件"
if [ $FAILED_COUNT -gt 0 ]; then
    warn "修复失败: $FAILED_COUNT 个文件"
fi

exit 0 