#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 找出所有枚举类文件
find chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums -name "*.java" > enum_files.txt
find chat-tag-system-api/src/main/java/com/ke/chat/tag/api/bean -name "*Enum.java" >> enum_files.txt

info "发现 $(wc -l < enum_files.txt) 个枚举文件"

count=0
# 对每个枚举文件进行处理
while IFS= read -r file; do
    count=$((count+1))
    info "处理第 $count 个文件: $file"
    
    # 检查文件中是否有损坏的导入语句
    if grep -q "nimport" "$file"; then
        info "文件 $file 中有损坏的导入语句，正在修复..."
        
        # 创建临时文件
        temp_file=$(mktemp)
        
        # 读取文件的每一行
        while IFS= read -r line; do
            # 检查行是否包含损坏的导入语句
            if [[ $line == *"nimport"* ]]; then
                # 替换损坏的导入语句
                fixed_line=$(echo "$line" | sed 's/nimport/\nimport/g')
                echo "$fixed_line" >> "$temp_file"
            else
                echo "$line" >> "$temp_file"
            fi
        done < "$file"
        
        # 替换原文件
        mv "$temp_file" "$file"
        info "成功修复 $file 中的导入语句"
    else
        info "文件 $file 中没有发现损坏的导入语句"
    fi
done < enum_files.txt

info "所有枚举文件的导入语句检查完成！"

# 现在，找出可能包含其他特殊字符的文件
info "检查特殊字符问题..."
grep -l "n@" $(cat enum_files.txt) > problem_files.txt

if [ -s problem_files.txt ]; then
    info "发现 $(wc -l < problem_files.txt) 个有特殊字符问题的文件"
    
    while IFS= read -r file; do
        info "修复文件 $file 中的特殊字符问题"
        
        # 替换各种特殊字符组合
        sed -i '' 's/n@/@/g' "$file"
        sed -i '' 's/n}//g' "$file"
        sed -i '' 's/n{/{/g' "$file"
        sed -i '' 's/n\;/;/g' "$file"
        sed -i '' 's/n\)/))/g' "$file"
        sed -i '' 's/n\(/(/g' "$file"
        
        info "完成修复: $file"
    done < problem_files.txt
else
    info "没有发现有特殊字符问题的文件"
fi

info "清理中间文件..."
rm -f enum_files.txt problem_files.txt

info "所有修复完成！" 