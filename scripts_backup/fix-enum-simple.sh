#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

header() {
    echo -e "\n${BLUE}==== $1 ====${NC}\n"
}

header "修复关键枚举文件"

# 修复KnowledgeSourceEnum.java
fix_knowledge_source_enum() {
    local file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/KnowledgeSourceEnum.java"
    if [ -f "$file" ]; then
        info "修复 $file"
        cp "$file" "${file}.bak"
        
        cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/3/1 11:11 上午
 **/
public enum KnowledgeSourceEnum {
    FAQ(0, "FAQ"),
    KMS(1, "KMS"),
    ONECARE(2, "ONECARE"),
    DOCUMENT(3, "文档"),
    KE_WIKI(4, "贝壳wiki"),
    QA_IMPORT(5, "问答导入"),
    MANUAL(6, "手动创建");

    public final int code;
    public final String message;

    KnowledgeSourceEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static KnowledgeSourceEnum getByCode(int code) {
        return Arrays.stream(KnowledgeSourceEnum.values())
                .filter(i -> i.code == code)
                .findFirst().orElse(null);
    }
}
EOF
        info "成功修复 $file"
    else
        error "未找到 $file"
    fi
}

# 修复WebSocketMessageTypeEnum.java
fix_websocket_message_type_enum() {
    local file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/WebSocketMessageTypeEnum.java"
    if [ -f "$file" ]; then
        info "修复 $file"
        cp "$file" "${file}.bak"
        
        cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * <AUTHOR>
 **/
public enum WebSocketMessageTypeEnum {
    CONNECT(0, "连接"),
    DISCONNECT(1, "断开连接");

    public final int code;
    public final String message;

    WebSocketMessageTypeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
EOF
        info "成功修复 $file"
    else
        error "未找到 $file"
    fi
}

# 修复RoleEnum.java
fix_role_enum() {
    local file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/team/RoleEnum.java"
    if [ -f "$file" ]; then
        info "修复 $file"
        cp "$file" "${file}.bak"
        
        cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.team;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/7/10 16:31
 **/
public enum RoleEnum {
    ADMIN(0, "管理员"),
    MEMBER(1, "成员");

    public final int code;
    public final String message;

    RoleEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static RoleEnum getByCode(int code) {
        return Arrays.stream(RoleEnum.values())
                .filter(i -> i.code == code)
                .findFirst().orElse(null);
    }
}
EOF
        info "成功修复 $file"
    else
        error "未找到 $file"
    fi
}

# 修复PartnerOrgTypeEnum.java
fix_partner_org_type_enum() {
    local file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/partner/PartnerOrgTypeEnum.java"
    if [ -f "$file" ]; then
        info "修复 $file"
        cp "$file" "${file}.bak"
        
        cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.partner;

import java.util.Arrays;

/**
 * 合作伙伴组织类型枚举
 * 
 * @author: zhounan26
 * @date: 2023/6/21
 */
public enum PartnerOrgTypeEnum {
    ENTERPRISE(0, "企业"),
    INDIVIDUAL(1, "个人");

    public final int code;
    public final String message;

    PartnerOrgTypeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static PartnerOrgTypeEnum getByCode(int code) {
        return Arrays.stream(PartnerOrgTypeEnum.values())
                .filter(i -> i.code == code)
                .findFirst().orElse(null);
    }
}
EOF
        info "成功修复 $file"
    else
        error "未找到 $file"
    fi
}

# 修复FileAccessModeEnum.java
fix_file_access_mode_enum() {
    local file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/file/FileAccessModeEnum.java"
    if [ -f "$file" ]; then
        info "修复 $file"
        cp "$file" "${file}.bak"
        
        cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.file;

/**
 * <AUTHOR>
 **/
public enum FileAccessModeEnum {
    PUBLIC(0, "公开"),
    PRIVATE(1, "私有");

    public final int code;
    public final String message;

    FileAccessModeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
EOF
        info "成功修复 $file"
    else
        error "未找到 $file"
    fi
}

# 修复ShareRecordTypeEnum.java
fix_share_record_type_enum() {
    local file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/ShareRecordTypeEnum.java"
    if [ -f "$file" ]; then
        info "修复 $file"
        cp "$file" "${file}.bak"
        
        cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/3/10 6:22 下午
 **/
public enum ShareRecordTypeEnum {
    CREATED(0, "创建"),
    AUTHORIZED(1, "授权");

    public final int code;
    public final String message;

    ShareRecordTypeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
EOF
        info "成功修复 $file"
    else
        error "未找到 $file"
    fi
}

# 修复ApplicationStatisticsDimensionalityEnum.java
fix_app_statistics_dimensionality_enum() {
    local file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/application/ApplicationStatisticsDimensionalityEnum.java"
    if [ -f "$file" ]; then
        info "修复 $file"
        cp "$file" "${file}.bak"
        
        cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.application;

/**
 * <AUTHOR>
 * @date 2023/12/26
 */
public enum ApplicationStatisticsDimensionalityEnum {
    DAILY(0, "每日"),
    WEEKLY(1, "每周"),
    MONTHLY(2, "每月");

    public final int code;
    public final String message;

    ApplicationStatisticsDimensionalityEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
EOF
        info "成功修复 $file"
    else
        error "未找到 $file"
    fi
}

# 修复RunStepDetailEnum.java
fix_run_step_detail_enum() {
    local file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/assistant/RunStepDetailEnum.java"
    if [ -f "$file" ]; then
        info "修复 $file"
        cp "$file" "${file}.bak"
        
        cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.assistant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/27 2:15 下午
 **/
@Getter
@AllArgsConstructor
public enum RunStepDetailEnum {
    MESSAGE_CREATION("message_creation", "消息创建"),
    TOOL_CALLS("tool_calls", "工具调用");

    private final String code;
    private final String desc;
}
EOF
        info "成功修复 $file"
    else
        error "未找到 $file"
    fi
}

# 修复BusinessEnum.java
fix_business_enum() {
    local file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/qyqk/BusinessEnum.java"
    if [ -f "$file" ]; then
        info "修复 $file"
        cp "$file" "${file}.bak"
        
        cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.qyqk;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/10/12 10:58 上午
 */
@Getter
@AllArgsConstructor
public enum BusinessEnum {
    B2B_PLATFORM("贝壳企业服务平台"),
    LPC_PLATFORM("链家合伙人社区"),
    KE_PLATFORM("贝壳平台");

    private final String desc;
}
EOF
        info "成功修复 $file"
    else
        error "未找到 $file"
    fi
}

# 执行修复
fix_knowledge_source_enum
fix_websocket_message_type_enum
fix_role_enum
fix_partner_org_type_enum
fix_file_access_mode_enum
fix_share_record_type_enum
fix_app_statistics_dimensionality_enum
fix_run_step_detail_enum
fix_business_enum

header "修复完成"
info "已修复关键枚举文件，现在您可以尝试重新构建项目" 