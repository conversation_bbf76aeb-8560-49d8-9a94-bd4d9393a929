#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

info "=== Lombok与Java兼容性修复工具 ==="

# 检查Java版本
JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | awk -F. '{print $1}')
info "检测到Java版本: $JAVA_VERSION"

if [ "$JAVA_VERSION" -ge 17 ]; then
    warn "检测到较新的Java版本，需要调整JVM参数以兼容Lombok"
    
    # 创建MAVEN_OPTS环境变量来添加必要的JVM参数
    export MAVEN_OPTS="--add-opens=jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED $MAVEN_OPTS"
    
    info "已设置MAVEN_OPTS以解决Lombok兼容性问题:"
    echo "$MAVEN_OPTS"
else
    info "Java版本低于17，无需特殊处理"
fi

# 检查pom.xml中的lombok版本
POM_FILE="pom.xml"
if [ -f "$POM_FILE" ]; then
    LOMBOK_VERSION=$(grep -A 3 "<artifactId>lombok</artifactId>" "$POM_FILE" | grep "<version>" | head -1 | sed -E 's/.*<version>(.*)<\/version>.*/\1/')
    
    if [ -n "$LOMBOK_VERSION" ]; then
        info "检测到Lombok版本: $LOMBOK_VERSION"
        
        # 版本号比较，判断是否需要升级Lombok
        if [[ "$LOMBOK_VERSION" < "1.18.16" && "$JAVA_VERSION" -ge 17 ]]; then
            warn "Lombok版本过低，建议升级到1.18.16或更高版本以兼容Java $JAVA_VERSION"
            info "您可以通过修改pom.xml文件升级Lombok版本"
        fi
    else
        warn "未检测到Lombok版本信息"
    fi
fi

# 运行Maven构建，使用之前设置好的环境变量
info "是否使用兼容模式运行Maven构建?"
read -p "运行构建? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    info "开始使用兼容模式构建..."
    
    # 构建命令
    BUILD_CMD="./bypass-maven-enhanced.sh"
    
    # 检查脚本是否存在
    if [ -f "$BUILD_CMD" ]; then
        chmod +x "$BUILD_CMD"
        $BUILD_CMD
    else
        error "找不到构建脚本: $BUILD_CMD"
        
        # 如果脚本不存在，则使用基本的Maven命令
        warn "使用基本Maven命令构建..."
        mvn clean package -DskipTests -Dmaven.resolver.transport.http.blocker.disabled=true -U
    fi
else
    info "已取消构建"
    info "您可以手动运行以下命令进行构建:"
    echo "MAVEN_OPTS=\"$MAVEN_OPTS\" ./bypass-maven-enhanced.sh"
fi 