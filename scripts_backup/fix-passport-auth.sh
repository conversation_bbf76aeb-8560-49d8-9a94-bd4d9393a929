#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

header() {
    echo -e "\n${BLUE}==== $1 ====${NC}\n"
}

header "Passport认证修复工具"

# 检查是否缺少拦截器文件
INTERCEPTOR_DIR="chat-tag-system-start/src/main/java/com/ke/chat/tag/interceptor"
PASSPORT_SERVICE="$INTERCEPTOR_DIR/PassportSessionService.java"
CHAT_TAG_INTERCEPTOR="$INTERCEPTOR_DIR/ChatTagInterceptor.java"

info "检查拦截器文件..."

MISSING_FILES=()
[ ! -f "$PASSPORT_SERVICE" ] && MISSING_FILES+=("$PASSPORT_SERVICE")
[ ! -f "$CHAT_TAG_INTERCEPTOR" ] && MISSING_FILES+=("$CHAT_TAG_INTERCEPTOR")

if [ ${#MISSING_FILES[@]} -gt 0 ]; then
    warn "检测到以下文件缺失:"
    for FILE in "${MISSING_FILES[@]}"; do
        echo "- $FILE"
    done
    
    # 询问是否创建缺失的文件
    read -p "是否创建缺失的拦截器文件? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # 确保目录存在
        mkdir -p "$INTERCEPTOR_DIR"
        
        # 创建PassportSessionService.java
        if [[ " ${MISSING_FILES[@]} " =~ " $PASSPORT_SERVICE " ]]; then
            info "创建 $PASSPORT_SERVICE..."
            
            cat > "$PASSPORT_SERVICE" << 'EOF'
package com.ke.chat.tag.interceptor;

import com.alibaba.fastjson.JSON;
import com.ke.auth.sdk.model.AuthToken;
import com.ke.auth.sdk.model.PermissionVO;
import com.ke.auth.sdk.passport.IPassportClient;
import com.ke.auth.sdk.passport.VerifyResult;
import com.ke.chat.tag.service.common.TokenService;
import com.lianjia.matrix.utils.StringUtils;
import com.lianjia.passport.common.context.AuthenticationContext;
import com.lianjia.passport.common.model.AuthorizeParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * Passport认证服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PassportSessionService {

    @Autowired
    private IPassportClient passportClient;

    @Autowired
    private TokenService tokenService;

    /**
     * 验证用户登录态并设置认证上下文
     *
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 是否认证成功
     */
    public boolean verifyLoginAndSetContext(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 从请求中获取token
            String token = extractToken(request);
            if (StringUtils.isBlank(token)) {
                log.warn("No token found in request");
                return false;
            }
            
            // 验证token
            VerifyResult result = passportClient.verify(token);
            if (result == null || !result.isSuccess()) {
                log.warn("Token verification failed: {}", result);
                return false;
            }
            
            // 设置认证上下文
            AuthToken authToken = result.getAuthToken();
            if (authToken == null) {
                log.warn("No auth token in verify result");
                return false;
            }
            
            AuthorizeParam authorizeParam = new AuthorizeParam();
            authorizeParam.setUserId(authToken.getUserId());
            authorizeParam.setUcid(authToken.getUcid());
            authorizeParam.setToken(token);
            
            // 设置用户权限
            List<PermissionVO> permissions = authToken.getPermissions();
            if (permissions != null && !permissions.isEmpty()) {
                List<String> permissionCodes = new ArrayList<>(permissions.size());
                for (PermissionVO permission : permissions) {
                    permissionCodes.add(permission.getCode());
                }
                authorizeParam.setPermissions(permissionCodes);
            }
            
            // 设置认证上下文
            AuthenticationContext.set(authorizeParam);
            
            // 更新TokenService中的用户信息
            tokenService.setUserInfo(authToken.getUserId(), authToken.getUcid(), token);
            
            log.debug("User authenticated: userId={}, ucid={}", authToken.getUserId(), authToken.getUcid());
            return true;
        } catch (Exception e) {
            log.error("Error verifying login and setting context", e);
            return false;
        }
    }
    
    /**
     * 从请求中提取认证token
     * 按照以下顺序查找: Authorization头、Cookie、请求参数
     */
    private String extractToken(HttpServletRequest request) {
        // 1. 从Authorization头中获取
        String token = request.getHeader("Authorization");
        if (StringUtils.isNotBlank(token)) {
            if (token.startsWith("Bearer ")) {
                token = token.substring(7);
            }
            return token;
        }
        
        // 2. 从Cookie中获取
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ("TOKEN".equals(cookie.getName()) || "token".equals(cookie.getName()) || 
                    "passport_token".equals(cookie.getName())) {
                    return cookie.getValue();
                }
            }
        }
        
        // 3. 从请求参数中获取
        token = request.getParameter("token");
        if (StringUtils.isNotBlank(token)) {
            return token;
        }
        
        // 未找到token
        return null;
    }
    
    /**
     * 清除认证上下文
     */
    public void clearContext() {
        AuthenticationContext.remove();
        tokenService.clear();
    }
}
EOF
            info "已创建 $PASSPORT_SERVICE"
        fi
        
        # 创建ChatTagInterceptor.java
        if [[ " ${MISSING_FILES[@]} " =~ " $CHAT_TAG_INTERCEPTOR " ]]; then
            info "创建 $CHAT_TAG_INTERCEPTOR..."
            
            cat > "$CHAT_TAG_INTERCEPTOR" << 'EOF'
package com.ke.chat.tag.interceptor;

import com.ke.chat.tag.service.common.TokenService;
import com.lianjia.matrix.utils.StringUtils;
import com.lianjia.passport.common.context.AuthenticationContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 聊天标签系统拦截器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ChatTagInterceptor implements HandlerInterceptor {

    private static final List<String> WHITELIST_URIS = Arrays.asList(
        "/api/health", 
        "/api/ping",
        "/api/login",
        "/api/callback",
        "/error"
    );

    @Autowired
    private PassportSessionService passportSessionService;

    @Autowired
    private TokenService tokenService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        
        // 检查是否在白名单中
        if (isInWhitelist(requestURI)) {
            log.debug("Request is in whitelist: {}", requestURI);
            return true;
        }
        
        // 验证登录状态并设置上下文
        boolean authenticated = passportSessionService.verifyLoginAndSetContext(request, response);
        if (!authenticated) {
            log.warn("Authentication failed for request: {}", requestURI);
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":401,\"message\":\"用户未登录或登录状态已失效\"}");
            return false;
        }
        
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // 不需要特殊处理
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清除线程本地变量，防止内存泄漏
        passportSessionService.clearContext();
    }

    /**
     * 检查请求URI是否在白名单中
     */
    private boolean isInWhitelist(String requestURI) {
        if (StringUtils.isBlank(requestURI)) {
            return false;
        }
        
        for (String whitelistUri : WHITELIST_URIS) {
            if (requestURI.startsWith(whitelistUri)) {
                return true;
            }
        }
        
        return false;
    }
}
EOF
            info "已创建 $CHAT_TAG_INTERCEPTOR"
        fi
        
        info "拦截器文件创建完成"
    else
        warn "未创建缺失的拦截器文件，请手动创建"
    fi
else
    info "拦截器文件已存在，无需创建"
fi

# 检查拦截器配置
CONFIG_FILE="chat-tag-system-start/src/main/java/com/ke/chat/tag/configuration/WebMvcConfig.java"
if [ -f "$CONFIG_FILE" ]; then
    info "检查拦截器配置文件: $CONFIG_FILE"
    
    # 检查是否配置了ChatTagInterceptor
    if ! grep -q "ChatTagInterceptor" "$CONFIG_FILE"; then
        warn "未在WebMvcConfig中找到ChatTagInterceptor配置"
        
        # 询问是否更新配置
        read -p "是否更新WebMvcConfig配置? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            # 创建备份
            cp "$CONFIG_FILE" "${CONFIG_FILE}.bak"
            info "已创建备份文件: ${CONFIG_FILE}.bak"
            
            # 尝试更新配置
            if grep -q "addInterceptors" "$CONFIG_FILE"; then
                # 已有addInterceptors方法，添加拦截器
                sed -i.tmp '/addInterceptors.*{/a \
        registry.addInterceptor(chatTagInterceptor).addPathPatterns("/**");' "$CONFIG_FILE"
            else
                # 没有addInterceptors方法，添加完整方法
                sed -i.tmp '/public class WebMvcConfig/a \
\
    @Autowired\
    private ChatTagInterceptor chatTagInterceptor;\
\
    @Override\
    public void addInterceptors(InterceptorRegistry registry) {\
        registry.addInterceptor(chatTagInterceptor).addPathPatterns("/**");\
    }' "$CONFIG_FILE"
            fi
            
            # 添加导入语句
            if ! grep -q "import.*ChatTagInterceptor" "$CONFIG_FILE"; then
                sed -i.tmp '/package com.ke.chat.tag.configuration;/a \
\
import com.ke.chat.tag.interceptor.ChatTagInterceptor;\
import org.springframework.beans.factory.annotation.Autowired;\
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;' "$CONFIG_FILE"
            fi
            
            rm -f "${CONFIG_FILE}.tmp"
            info "已更新WebMvcConfig配置"
        else
            info "未更新WebMvcConfig配置，请手动更新"
        fi
    else
        info "WebMvcConfig中已配置ChatTagInterceptor"
    fi
else
    warn "未找到WebMvcConfig配置文件: $CONFIG_FILE"
    
    # 询问是否创建
    read -p "是否创建WebMvcConfig配置文件? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # 确保目录存在
        mkdir -p "$(dirname "$CONFIG_FILE")"
        
        # 创建配置文件
        cat > "$CONFIG_FILE" << 'EOF'
package com.ke.chat.tag.configuration;

import com.ke.chat.tag.interceptor.ChatTagInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC配置
 *
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private ChatTagInterceptor chatTagInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(chatTagInterceptor).addPathPatterns("/**");
    }
}
EOF
        info "已创建WebMvcConfig配置文件: $CONFIG_FILE"
    else
        warn "未创建WebMvcConfig配置文件，请手动创建"
    fi
fi

# 检查是否引入了Passport依赖
POM_FILE="pom.xml"
if [ -f "$POM_FILE" ]; then
    info "检查Passport依赖..."
    
    if ! grep -q "passport-client" "$POM_FILE" && ! grep -q "com.lianjia.passport" "$POM_FILE"; then
        warn "未找到Passport依赖"
        
        # 询问是否添加依赖
        read -p "是否添加Passport依赖? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            # 创建备份
            cp "$POM_FILE" "${POM_FILE}.passport.bak"
            info "已创建备份文件: ${POM_FILE}.passport.bak"
            
            # 检查是否存在dependencyManagement部分
            if grep -q "<dependencyManagement>" "$POM_FILE"; then
                # 在dependencyManagement/dependencies中添加
                sed -i.tmp '/<\/dependencies>/ {x; s/$/\n        <!-- Passport认证依赖 -->\n        <dependency>\n            <groupId>com.lianjia.passport<\/groupId>\n            <artifactId>passport-client<\/artifactId>\n            <version>1.0.0<\/version>\n        <\/dependency>/; x}' "$POM_FILE"
            else
                # 找到最后一个</dependency>添加
                sed -i.tmp '/<\/dependencies>/ {x; s/$/\n    <!-- Passport认证依赖 -->\n    <dependency>\n        <groupId>com.lianjia.passport<\/groupId>\n        <artifactId>passport-client<\/artifactId>\n        <version>1.0.0<\/version>\n    <\/dependency>/; x}' "$POM_FILE"
            fi
            
            rm -f "${POM_FILE}.tmp"
            info "已添加Passport依赖"
            
            # 提示可能需要调整版本
            warn "请检查添加的Passport依赖版本是否正确，可能需要根据实际情况调整"
        else
            info "未添加Passport依赖，请手动添加"
        fi
    else
        info "已找到Passport依赖"
    fi
else
    error "未找到pom.xml文件，无法检查依赖"
fi

# 检查application.properties配置
APP_PROPS_FILE="chat-tag-system-start/src/main/resources/application.properties"
if [ -f "$APP_PROPS_FILE" ]; then
    info "检查应用配置文件: $APP_PROPS_FILE"
    
    # 检查是否有Passport相关配置
    if ! grep -q "passport" "$APP_PROPS_FILE"; then
        warn "未找到Passport相关配置"
        
        # 询问是否添加配置
        read -p "是否添加Passport配置? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            # 添加配置
            cat >> "$APP_PROPS_FILE" << 'EOF'

# Passport认证配置
passport.client.app-id=your-app-id
passport.client.app-secret=your-app-secret
passport.client.api-url=https://passport.ke.com/api
passport.client.login-url=https://passport.ke.com/login
passport.client.logout-url=https://passport.ke.com/logout
passport.client.callback-url=http://localhost:8080/api/callback
EOF
            info "已添加Passport配置"
            warn "请根据实际情况修改Passport配置中的应用ID、密钥和URL"
        else
            info "未添加Passport配置，请手动添加"
        fi
    else
        info "已找到Passport相关配置"
    fi
else
    warn "未找到application.properties文件，无法检查配置"
fi

header "处理完成"
info "Passport认证配置修复完成，建议执行以下操作:"
echo "1. 检查拦截器代码是否符合项目需求"
echo "2. 确认WebMvcConfig配置是否正确"
echo "3. 验证Passport依赖版本是否适配"
echo "4. 更新application.properties中的Passport配置为正确的值"
echo "5. 重新构建并运行项目，测试认证流程" 