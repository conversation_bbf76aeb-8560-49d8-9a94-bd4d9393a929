#!/bin/bash

# 创建临时修复脚本，避免XML标签问题

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 临时文件
TEMP_XML="/tmp/maven_settings_temp.xml"

# 第一步：创建基本框架
info "创建Maven设置文件..."

echo '<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0">
  <mirrors>
    <mirror>
      <id>lianjia-group-mirror</id>
      <mirrorOf>*</mirrorOf>
      <url>http://maven.lianjia.com/content/groups/Lianjia-Group</url>
    </mirror>
  </mirrors>
  <profiles>
    <profile>
      <id>lianjia-profile</id>
      <repositories>
        <repository>
          <id>lianjia-group</id>
          <url>http://maven.lianjia.com/content/groups/Lianjia-Group</url>
          <releases><enabled>true</enabled></releases>
          <snapshots><enabled>true</enabled></snapshots>
        </repository>
      </repositories>
      <pluginRepositories>
        <pluginRepository>
          <id>lianjia-group</id>
          <url>http://maven.lianjia.com/content/groups/Lianjia-Group</url>
          <releases><enabled>true</enabled></releases>
          <snapshots><enabled>true</enabled></snapshots>
        </pluginRepository>
      </pluginRepositories>
    </profile>
    <profile>
      <id>disable-http-blocker</id>
      <properties>
        <maven.resolver.transport.http.blocker.disabled>true</maven.resolver.transport.http.blocker.disabled>
      </properties>
    </profile>
  </profiles>
  <activeProfiles>
    <activeProfile>lianjia-profile</activeProfile>
    <activeProfile>disable-http-blocker</activeProfile>
  </activeProfiles>
</settings>' > $TEMP_XML

# 第二步：添加名称标签
# 使用sed命令插入name标签 - 这样可以避免编辑器替换问题
sed -i '' 's/<mirror>/&\n      <name>Lianjia Group Repository<\/name>/' $TEMP_XML
sed -i '' 's/<repository>/&\n          <name>Lianjia Group Repository<\/name>/' $TEMP_XML
sed -i '' 's/<pluginRepository>/&\n          <name>Lianjia Group Repository<\/name>/' $TEMP_XML

# 第三步：应用到用户设置
USER_SETTINGS="$HOME/.m2/settings.xml"
BACKUP_SETTINGS="$HOME/.m2/settings.xml.bak.$(date +%Y%m%d%H%M%S)"

# 备份现有设置
if [ -f "$USER_SETTINGS" ]; then
    info "备份现有Maven设置到 $BACKUP_SETTINGS"
    cp "$USER_SETTINGS" "$BACKUP_SETTINGS"
fi

# 确保目录存在
mkdir -p "$HOME/.m2"

# 复制新设置
cp $TEMP_XML "$USER_SETTINGS"
info "已更新Maven设置: $USER_SETTINGS"

# 显示文件内容以验证
info "设置文件内容:"
cat "$USER_SETTINGS"

# 清理
rm $TEMP_XML

# 指导下一步
info "现在可以尝试构建项目:"
echo "mvn clean package -DskipTests"

# 询问是否立即构建
read -p "是否立即构建项目？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    info "开始构建项目..."
    mvn clean package -DskipTests
    if [ $? -eq 0 ]; then
        info "构建成功！"
    else
        error "构建失败，请检查错误信息"
    fi
fi 