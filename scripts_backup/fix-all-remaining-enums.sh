#!/bin/bash

# 创建修复枚举文件的函数
fix_enum_file() {
    local file=$1
    echo "处理文件: $file"
    
    # 使用临时文件
    local temp_file="${file}.tmp"
    
    # 读取文件内容
    content=$(cat "$file")
    
    # 提取枚举名称
    enum_name=$(grep -o "public enum [A-Za-z0-9_]*" "$file" | sed 's/public enum //')
    
    # 提取枚举常量部分 (通常是带有注释的枚举常量定义)
    enum_constants=$(grep -A 50 -E '[A-Z_]+\([^)]*\),' "$file" | sed -n '/[A-Z_]\+(/,/;/p')
    
    # 如果没有找到枚举常量，尝试另一种模式
    if [ -z "$enum_constants" ]; then
        enum_constants=$(grep -A 50 -E '[A-Z_]+\("' "$file" | sed -n '/[A-Z_]\+(/,/;/p')
    fi
    
    # 提取构造函数和其他方法
    constructor_and_methods=$(grep -A 20 -E "${enum_name}\([^)]*\) \{" "$file" | sed -n "/${enum_name}(/,/}/p")
    
    # 提取字段声明
    fields=$(grep -E 'private [A-Za-z0-9_<>]+ [a-zA-Z0-9_]+;' "$file")
    
    # 创建新的枚举文件内容
    {
        # 提取包名和导入语句
        grep -E '^package |^import ' "$file"
        
        # 提取类注释和注解
        grep -E '^/\*\*|^ \*|^@[A-Za-z]' "$file"
        
        # 添加枚举声明，但不包括 {
        grep -E 'public enum [A-Za-z0-9_]+' "$file" | sed 's/ {//'
        
        # 添加 {
        echo " {"
        
        # 添加枚举常量
        echo "$enum_constants"
        
        # 添加字段声明
        echo "$fields"
        
        # 添加构造函数和方法
        echo "$constructor_and_methods"
        
        # 添加类结束的 }
        echo "}"
    } > "$temp_file"
    
    # 替换原文件
    mv "$temp_file" "$file"
}

# 查找所有枚举文件并修复
find chat-tag-system-api/src/main/java -name "*.java" -exec grep -l "public enum" {} \; | while read -r file; do
    fix_enum_file "$file"
done

echo "所有枚举文件修复完成!" 