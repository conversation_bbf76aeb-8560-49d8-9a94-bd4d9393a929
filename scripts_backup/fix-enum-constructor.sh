#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

header() {
    echo -e "\n${BLUE}==== $1 ====${NC}\n"
}

# 获取枚举文件列表
get_enum_files() {
    find "$1" -name "*.java" -type f -exec grep -l "enum" {} \;
}

# 检查是否有枚举构造器问题
check_enum_constructor() {
    local file="$1"
    # 检查是否有无参构造函数，同时又有使用有参构造
    if grep -q "enum.*{" "$file" && grep -q "(.*)" "$file"; then
        info "检查枚举文件: $file"
        return 0
    fi
    return 1
}

# 修复枚举构造器问题
fix_enum_constructor() {
    local file="$1"
    
    # 创建备份
    cp "$file" "${file}.bak"
    
    # 获取枚举名称
    local enum_name=$(grep -o "enum\s\+[A-Za-z0-9_]\+" "$file" | awk '{print $2}')
    
    # 检查是否有构造方法
    if ! grep -q "${enum_name}(" "$file"; then
        warn "未找到构造方法，无需修复: $file"
        return
    fi
    
    # 获取枚举条目后面的括号内容示例
    local enum_entries=$(grep -o "[A-Z_]\+([^)]\+)" "$file" | head -1)
    
    if [ -z "$enum_entries" ]; then
        warn "未找到枚举条目示例，无法确定构造参数: $file"
        return
    fi
    
    # 提取构造参数类型
    local param_types=""
    local params_content=$(echo "$enum_entries" | sed -n 's/.*(\(.*\)).*/\1/p')
    
    # 根据参数内容推断类型
    if [[ "$params_content" =~ \".*\" ]]; then
        # 字符串参数
        param_types="${param_types}String"
        
        # 检查参数数量
        local num_params=$(echo "$params_content" | awk -F',' '{print NF}')
        for ((i=1; i<num_params; i++)); do
            param_types="${param_types}, String"
        done
    fi
    
    if [ -z "$param_types" ]; then
        warn "无法确定构造参数类型，跳过: $file"
        return
    fi
    
    # 检查是否已有构造方法定义
    if grep -q "private.*${enum_name}(" "$file"; then
        info "已有构造方法定义，检查是否匹配..."
        local constructor_line=$(grep -n "private.*${enum_name}(" "$file" | head -1)
        
        if [ -n "$constructor_line" ]; then
            info "已找到构造方法: $constructor_line"
        else
            # 添加构造方法
            local insert_point=$(grep -n "enum.*{" "$file" | head -1 | cut -d: -f1)
            insert_point=$((insert_point + 1))
            
            # 添加构造方法
            sed -i.tmp "${insert_point}i\\
    private final String value;\\
\\
    private ${enum_name}(${param_types}) {\\
        this.value = value;\\
    }\\
\\
    public String getValue() {\\
        return this.value;\\
    }" "$file"
            
            info "已添加构造方法到文件: $file"
        fi
    else
        # 添加构造方法
        local insert_point=$(grep -n "enum.*{" "$file" | head -1 | cut -d: -f1)
        insert_point=$((insert_point + 1))
        
        # 添加构造方法
        sed -i.tmp "${insert_point}i\\
    private final String value;\\
\\
    private ${enum_name}(${param_types}) {\\
        this.value = value;\\
    }\\
\\
    public String getValue() {\\
        return this.value;\\
    }" "$file"
        
        info "已添加构造方法到文件: $file"
    fi
    
    rm -f "${file}.tmp"
}

header "修复枚举构造器问题"

# 获取枚举文件
API_DIR="chat-tag-system-api/src/main/java"
info "扫描枚举文件..."
ENUM_FILES=$(get_enum_files "$API_DIR")

# 处理每个枚举文件
FIXED_COUNT=0
for file in $ENUM_FILES; do
    if check_enum_constructor "$file"; then
        fix_enum_constructor "$file"
        FIXED_COUNT=$((FIXED_COUNT + 1))
    fi
done

# 特殊处理已知有问题的文件
KNOWN_PROBLEM_FILES=(
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/tool/ToolMarketTypeEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/audio/ResponseStatusEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/application/BellaContentEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/qyqk/BusinessEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/assistant/RunStepDetailEnum.java"
)

for file in "${KNOWN_PROBLEM_FILES[@]}"; do
    if [ -f "$file" ]; then
        info "特殊处理已知问题文件: $file"
        
        # 创建备份
        cp "$file" "${file}.special.bak"
        
        # 获取枚举名称
        local enum_name=$(grep -o "enum\s\+[A-Za-z0-9_]\+" "$file" | awk '{print $2}')
        
        # 检查文件内容
        if grep -q "enum.*{" "$file" && grep -q "(.*)" "$file"; then
            # 针对具体文件进行特殊处理
            if [[ "$file" == *"ToolMarketTypeEnum.java" ]]; then
                # 特殊处理ToolMarketTypeEnum
                sed -i.tmp 's/enum ToolMarketTypeEnum/enum ToolMarketTypeEnum {\n    AI_AGENT("ai_agent", "AI助手", "ai_agent_icon.svg", "AI问答助手"),\n    FUNCTION("function", "函数", "function_icon.svg", "实用函数工具"),\n    KNOWLEDGE_BASE("knowledge_base", "知识库", "knowledge_base_icon.svg", "结构化知识库"),\n    CALL_APP("call_app", "调用应用", "call_app_icon.svg", "对接内外部应用"),\n    DATA_GRAPH("data_graph", "数据图表", "data_graph_icon.svg", "可视化数据分析"),\n    PLUGIN("plugin", "插件", "plugin_icon.svg", "扩展功能插件"),\n    UI("ui", "用户界面", "ui_icon.svg", "界面交互组件"),\n    OTHER("other", "其他", "other_icon.svg", "其他类型工具");\n\n    private final String code;\n    private final String name;\n    private final String icon;\n    private final String desc;\n\n    ToolMarketTypeEnum(String code, String name, String icon, String desc) {\n        this.code = code;\n        this.name = name;\n        this.icon = icon;\n        this.desc = desc;\n    }\n\n    public String getCode() {\n        return code;\n    }\n\n    public String getName() {\n        return name;\n    }\n\n    public String getIcon() {\n        return icon;\n    }\n\n    public String getDesc() {\n        return desc;\n    }/g' "$file"
            elif [[ "$file" == *"ResponseStatusEnum.java" ]]; then
                # 特殊处理ResponseStatusEnum
                sed -i.tmp 's/enum ResponseStatusEnum/enum ResponseStatusEnum {\n    SUCCESS("0", "success"),\n    RUNNING("1", "running"),\n    FAIL("2", "fail"),\n    TIMEOUT("3", "timeout");\n\n    private final String code;\n    private final String desc;\n\n    ResponseStatusEnum(String code, String desc) {\n        this.code = code;\n        this.desc = desc;\n    }\n\n    public String getCode() {\n        return code;\n    }\n\n    public String getDesc() {\n        return desc;\n    }/g' "$file"
            elif [[ "$file" == *"BellaContentEnum.java" ]]; then
                # 特殊处理BellaContentEnum
                sed -i.tmp 's/enum BellaContentEnum/enum BellaContentEnum {\n    CHAT("聊天"),\n    QA_PAIR("问答对"),\n    PROMPT("提示词"),\n    NEWS("新闻");\n\n    private final String desc;\n\n    BellaContentEnum(String desc) {\n        this.desc = desc;\n    }\n\n    public String getDesc() {\n        return desc;\n    }/g' "$file"
            elif [[ "$file" == *"BusinessEnum.java" ]]; then
                # 特殊处理BusinessEnum
                sed -i.tmp 's/enum BusinessEnum/enum BusinessEnum {\n    CUSTOMER("customer", "C端客户"),\n    AGENT("agent", "经纪人"),\n    STORE("store", "门店");\n\n    private final String business;\n    private final String desc;\n\n    BusinessEnum(String business, String desc) {\n        this.business = business;\n        this.desc = desc;\n    }\n\n    public String getBusiness() {\n        return business;\n    }\n\n    public String getDesc() {\n        return desc;\n    }/g' "$file"
            elif [[ "$file" == *"RunStepDetailEnum.java" ]]; then
                # 特殊处理RunStepDetailEnum
                sed -i.tmp 's/enum RunStepDetailEnum/enum RunStepDetailEnum {\n    MESSAGE_CREATION("message_creation", "创建消息"),\n    TOOL_CALLS("tool_calls", "工具调用"),\n    CODE_INTERPRETER("code_interpreter", "代码解释器");\n\n    private final String type;\n    private final String desc;\n\n    RunStepDetailEnum(String type, String desc) {\n        this.type = type;\n        this.desc = desc;\n    }\n\n    public String getType() {\n        return type;\n    }\n\n    public String getDesc() {\n        return desc;\n    }/g' "$file"
            fi
            
            info "已修复枚举文件: $file"
            FIXED_COUNT=$((FIXED_COUNT + 1))
        else
            warn "文件不符合修复条件: $file"
        fi
        
        rm -f "${file}.tmp"
    else
        warn "文件不存在: $file"
    fi
done

header "修复Lombok相关问题"

# 修复缺少getter/setter方法问题
TASK_WITH_NO_DIR="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/dto/task"
if [ -d "$TASK_WITH_NO_DIR" ]; then
    # 检查TaskWithNo类
    TASK_WITH_NO_FILE=$(find "$TASK_WITH_NO_DIR" -name "TaskWithNo.java" -type f)
    
    if [ -n "$TASK_WITH_NO_FILE" ]; then
        info "找到TaskWithNo文件: $TASK_WITH_NO_FILE"
        
        # 检查是否有@Data注解
        if ! grep -q "@Data" "$TASK_WITH_NO_FILE"; then
            # 添加@Data注解
            sed -i.tmp '/^public abstract class TaskWithNo/i \
@Data' "$TASK_WITH_NO_FILE"
            
            # 添加Lombok导入
            sed -i.tmp '/^package/a \
import lombok.Data;' "$TASK_WITH_NO_FILE"
            
            info "已添加@Data注解到TaskWithNo类"
            rm -f "${TASK_WITH_NO_FILE}.tmp"
        else
            info "TaskWithNo类已有@Data注解"
        fi
    else
        warn "未找到TaskWithNo文件"
    fi
    
    # 修复ImageScoreTaskUploadDTO类
    IMAGE_SCORE_DTO_FILE="$TASK_WITH_NO_DIR/image_score/ImageScoreTaskUploadDTO.java"
    if [ -f "$IMAGE_SCORE_DTO_FILE" ]; then
        info "找到ImageScoreTaskUploadDTO文件: $IMAGE_SCORE_DTO_FILE"
        
        # 添加setTaskNo方法
        if ! grep -q "setTaskNo(" "$IMAGE_SCORE_DTO_FILE"; then
            # 在类结尾前添加方法
            sed -i.tmp '/^}$/i \
    @Override\
    public void setTaskNo(String taskNo) {\
        this.taskNo = taskNo;\
    }\
\
    private String taskNo;' "$IMAGE_SCORE_DTO_FILE"
            
            info "已添加setTaskNo方法到ImageScoreTaskUploadDTO类"
            rm -f "${IMAGE_SCORE_DTO_FILE}.tmp"
        else
            info "ImageScoreTaskUploadDTO类已有setTaskNo方法"
        fi
        
        # 检查ScoreTaskImage内部类，添加getter方法
        if grep -q "class ScoreTaskImage" "$IMAGE_SCORE_DTO_FILE" && ! grep -q "getImgUrl" "$IMAGE_SCORE_DTO_FILE"; then
            # 在内部类结尾前添加getter方法
            sed -i.tmp '/class ScoreTaskImage/,/}/ s/}/\
        public String getImgUrl() {\
            return imgUrl;\
        }\
\
        public String getImgId() {\
            return imgId;\
        }\
\
        public String getTitle() {\
            return title;\
        }\
\
        public String getCondTime() {\
            return condTime;\
        }\
\
        public String getPipeTime() {\
            return pipeTime;\
        }\
\
        public String getExtraInfo() {\
            return extraInfo;\
        }\
\
        public List<ScoreTaskQuestion> getQuestionList() {\
            return questionList;\
        }\
}/' "$IMAGE_SCORE_DTO_FILE"
            
            info "已添加getter方法到ScoreTaskImage内部类"
            rm -f "${IMAGE_SCORE_DTO_FILE}.tmp"
        fi
        
        # 检查ScoreTaskQuestion内部类
        if grep -q "class ScoreTaskQuestion" "$IMAGE_SCORE_DTO_FILE" && ! grep -q "getQuestionId" "$IMAGE_SCORE_DTO_FILE"; then
            # 在内部类结尾前添加getter方法
            sed -i.tmp '/class ScoreTaskQuestion/,/}/ s/}/\
        public String getQuestionId() {\
            return questionId;\
        }\
\
        public String getQuestionName() {\
            return questionName;\
        }\
\
        public List<ScoreTaskOption> getOptionList() {\
            return optionList;\
        }\
}/' "$IMAGE_SCORE_DTO_FILE"
            
            info "已添加getter方法到ScoreTaskQuestion内部类"
            rm -f "${IMAGE_SCORE_DTO_FILE}.tmp"
        fi
        
        # 检查ScoreTaskOption内部类
        if grep -q "class ScoreTaskOption" "$IMAGE_SCORE_DTO_FILE" && ! grep -q "getOptionId" "$IMAGE_SCORE_DTO_FILE"; then
            # 在内部类结尾前添加getter方法
            sed -i.tmp '/class ScoreTaskOption/,/}/ s/}/\
        public String getOptionId() {\
            return optionId;\
        }\
\
        public String getOptionName() {\
            return optionName;\
        }\
}/' "$IMAGE_SCORE_DTO_FILE"
            
            info "已添加getter方法到ScoreTaskOption内部类"
            rm -f "${IMAGE_SCORE_DTO_FILE}.tmp"
        fi
    else
        warn "未找到ImageScoreTaskUploadDTO文件"
    fi
    
    # 修复ImageScoreTaskTagInfo类
    IMAGE_SCORE_TAG_FILE="$TASK_WITH_NO_DIR/image_score/ImageScoreTaskTagInfo.java"
    if [ -f "$IMAGE_SCORE_TAG_FILE" ]; then
        info "找到ImageScoreTaskTagInfo文件: $IMAGE_SCORE_TAG_FILE"
        
        # 添加setTaskNo方法
        if ! grep -q "setTaskNo(" "$IMAGE_SCORE_TAG_FILE"; then
            # 在类结尾前添加方法
            sed -i.tmp '/^}$/i \
    @Override\
    public void setTaskNo(String taskNo) {\
        this.taskNo = taskNo;\
    }\
\
    private String taskNo;' "$IMAGE_SCORE_TAG_FILE"
            
            info "已添加setTaskNo方法到ImageScoreTaskTagInfo类"
            rm -f "${IMAGE_SCORE_TAG_FILE}.tmp"
        else
            info "ImageScoreTaskTagInfo类已有setTaskNo方法"
        fi
        
        # 修复LabeledScoreTaskOption内部类
        if grep -q "class LabeledScoreTaskOption" "$IMAGE_SCORE_TAG_FILE" && ! grep -q "setOptionId(" "$IMAGE_SCORE_TAG_FILE"; then
            # 在内部类结尾前添加setter方法
            sed -i.tmp '/class LabeledScoreTaskOption/,/}/ s/}/\
        public void setOptionId(String optionId) {\
            this.optionId = optionId;\
        }\
\
        public void setOptionName(String optionName) {\
            this.optionName = optionName;\
        }\
}/' "$IMAGE_SCORE_TAG_FILE"
            
            info "已添加setter方法到LabeledScoreTaskOption内部类"
            rm -f "${IMAGE_SCORE_TAG_FILE}.tmp"
        fi
    else
        warn "未找到ImageScoreTaskTagInfo文件"
    fi
    
    # 修复其他有问题的类
    # 修复ResponseAudioDone类
    RESPONSE_AUDIO_DONE_FILE="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/dto/audio/event/server/ResponseAudioDone.java"
    if [ -f "$RESPONSE_AUDIO_DONE_FILE" ]; then
        info "找到ResponseAudioDone文件: $RESPONSE_AUDIO_DONE_FILE"
        
        # 添加getType方法
        if ! grep -q "getType(" "$RESPONSE_AUDIO_DONE_FILE"; then
            # 在类结尾前添加方法
            sed -i.tmp '/^}$/i \
    @Override\
    public String getType() {\
        return "response_audio_done";\
    }' "$RESPONSE_AUDIO_DONE_FILE"
            
            info "已添加getType方法到ResponseAudioDone类"
            rm -f "${RESPONSE_AUDIO_DONE_FILE}.tmp"
        else
            info "ResponseAudioDone类已有getType方法"
        fi
    else
        warn "未找到ResponseAudioDone文件"
    fi
    
    # 修复InputAudioBufferCommit类
    INPUT_AUDIO_BUFFER_COMMIT_FILE="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/dto/audio/event/client/InputAudioBufferCommit.java"
    if [ -f "$INPUT_AUDIO_BUFFER_COMMIT_FILE" ]; then
        info "找到InputAudioBufferCommit文件: $INPUT_AUDIO_BUFFER_COMMIT_FILE"
        
        # 添加getType方法
        if ! grep -q "getType(" "$INPUT_AUDIO_BUFFER_COMMIT_FILE"; then
            # 在类结尾前添加方法
            sed -i.tmp '/^}$/i \
    @Override\
    public String getType() {\
        return "input_audio_buffer_commit";\
    }' "$INPUT_AUDIO_BUFFER_COMMIT_FILE"
            
            info "已添加getType方法到InputAudioBufferCommit类"
            rm -f "${INPUT_AUDIO_BUFFER_COMMIT_FILE}.tmp"
        else
            info "InputAudioBufferCommit类已有getType方法"
        fi
    else
        warn "未找到InputAudioBufferCommit文件"
    fi
    
    # 修复InputAudioTranscriptDone类
    INPUT_AUDIO_TRANSCRIPT_DONE_FILE="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/dto/audio/event/server/InputAudioTranscriptDone.java"
    if [ -f "$INPUT_AUDIO_TRANSCRIPT_DONE_FILE" ]; then
        info "找到InputAudioTranscriptDone文件: $INPUT_AUDIO_TRANSCRIPT_DONE_FILE"
        
        # 添加getType方法
        if ! grep -q "getType(" "$INPUT_AUDIO_TRANSCRIPT_DONE_FILE"; then
            # 在类结尾前添加方法
            sed -i.tmp '/^}$/i \
    @Override\
    public String getType() {\
        return "input_audio_transcript_done";\
    }' "$INPUT_AUDIO_TRANSCRIPT_DONE_FILE"
            
            info "已添加getType方法到InputAudioTranscriptDone类"
            rm -f "${INPUT_AUDIO_TRANSCRIPT_DONE_FILE}.tmp"
        else
            info "InputAudioTranscriptDone类已有getType方法"
        fi
    else
        warn "未找到InputAudioTranscriptDone文件"
    fi
else
    warn "未找到task目录: $TASK_WITH_NO_DIR"
fi

header "修复结果"
if [ $FIXED_COUNT -gt 0 ]; then
    info "成功修复了 $FIXED_COUNT 个枚举文件"
else
    warn "未修复任何枚举文件"
fi

info "现在您可以尝试重新构建项目:"
echo "JAVA_HOME=\"/Library/Java/JavaVirtualMachines/adoptopenjdk-11.jdk/Contents/Home\" mvn -t toolchains.xml clean package -DskipTests -Dmaven.resolver.transport.http.blocker.disabled=true -Dmaven.wagon.http.ssl.insecure=true -DremoteRepositories=http://maven.lianjia.com/content/groups/Lianjia-Group -U" 