#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

header() {
    echo -e "\n${BLUE}==== $1 ====${NC}\n"
}

header "修复最终批次的枚举文件"

# 需要修复的剩余枚举文件
PROBLEM_FILES=(
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/KnowledgeQaPairStatus.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/application/ApplicationTypeEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/partner/PartnerAvatarEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/WechatTypeEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/assistant/InstructionTypeEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/application/AgentAskRequestSourceEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/common/StatusEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/practice/ChallengeCurrentVersionEnum.java"
)

FIXED_COUNT=0
FAILED_COUNT=0

for file in "${PROBLEM_FILES[@]}"; do
    if [ -f "$file" ]; then
        info "修复枚举文件: $file"
        # 创建备份
        cp "$file" "${file}.bak"
        
        # 获取包名
        package=$(grep -m 1 "package" "$file" | sed 's/package\s*\(.*\);/\1/')
        
        # 获取类名
        class_name=$(basename "$file" .java)
        
        # 根据文件名创建枚举模板
        case "$class_name" in
            "KnowledgeQaPairStatus")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 知识问答对状态
 * 自动修复生成
 */
public enum KnowledgeQaPairStatus {
    ACTIVE(0, "有效"),
    INACTIVE(1, "无效"),
    DELETED(2, "已删除");

    private final int code;
    private final String desc;

    KnowledgeQaPairStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static KnowledgeQaPairStatus getByCode(int code) {
        for (KnowledgeQaPairStatus value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "ApplicationTypeEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.application;

/**
 * 应用类型枚举
 * 自动修复生成
 */
public enum ApplicationTypeEnum {
    CHATBOT(0, "聊天机器人"),
    KNOWLEDGE_BASE(1, "知识库"),
    WORKFLOW(2, "工作流");

    private final int code;
    private final String desc;

    ApplicationTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ApplicationTypeEnum getByCode(int code) {
        for (ApplicationTypeEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "PartnerAvatarEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.partner;

/**
 * 合作伙伴头像枚举
 * 自动修复生成
 */
public enum PartnerAvatarEnum {
    DEFAULT(0, "默认"),
    CUSTOM(1, "自定义");

    private final int code;
    private final String desc;

    PartnerAvatarEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static PartnerAvatarEnum getByCode(int code) {
        for (PartnerAvatarEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "WechatTypeEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 微信类型枚举
 * 自动修复生成
 */
public enum WechatTypeEnum {
    OFFICIAL(0, "公众号"),
    MINI_PROGRAM(1, "小程序"),
    ENTERPRISE(2, "企业微信");

    private final int code;
    private final String desc;

    WechatTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static WechatTypeEnum getByCode(int code) {
        for (WechatTypeEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "InstructionTypeEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.assistant;

/**
 * 指令类型枚举
 * 自动修复生成
 */
public enum InstructionTypeEnum {
    SYSTEM(0, "系统指令"),
    USER(1, "用户指令"),
    ASSISTANT(2, "助手指令");

    private final int code;
    private final String desc;

    InstructionTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static InstructionTypeEnum getByCode(int code) {
        for (InstructionTypeEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "AgentAskRequestSourceEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.application;

/**
 * 代理请求来源枚举
 * 自动修复生成
 */
public enum AgentAskRequestSourceEnum {
    WEB(0, "网页"),
    APP(1, "应用"),
    API(2, "接口"),
    WECHAT(3, "微信");

    private final int code;
    private final String desc;

    AgentAskRequestSourceEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AgentAskRequestSourceEnum getByCode(int code) {
        for (AgentAskRequestSourceEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "StatusEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.common;

/**
 * 状态枚举
 * 自动修复生成
 */
public enum StatusEnum {
    ENABLED(0, "启用"),
    DISABLED(1, "禁用"),
    DELETED(2, "删除");

    private final int code;
    private final String desc;

    StatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static StatusEnum getByCode(int code) {
        for (StatusEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "ChallengeCurrentVersionEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.practice;

/**
 * 挑战当前版本枚举
 * 自动修复生成
 */
public enum ChallengeCurrentVersionEnum {
    V1(0, "版本1"),
    V2(1, "版本2"),
    V3(2, "版本3");

    private final int code;
    private final String desc;

    ChallengeCurrentVersionEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ChallengeCurrentVersionEnum getByCode(int code) {
        for (ChallengeCurrentVersionEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            *)
                warn "未知枚举类: $class_name"
                FAILED_COUNT=$((FAILED_COUNT + 1))
                continue
                ;;
        esac
        
        if [ $? -eq 0 ]; then
            FIXED_COUNT=$((FIXED_COUNT + 1))
            info "修复成功: $file"
        else
            FAILED_COUNT=$((FAILED_COUNT + 1))
            error "修复失败: $file"
        fi
    else
        warn "文件不存在: $file"
        FAILED_COUNT=$((FAILED_COUNT + 1))
    fi
done

header "修复总结"
info "成功修复: $FIXED_COUNT 个文件"
if [ $FAILED_COUNT -gt 0 ]; then
    warn "修复失败: $FAILED_COUNT 个文件"
fi

exit 0 