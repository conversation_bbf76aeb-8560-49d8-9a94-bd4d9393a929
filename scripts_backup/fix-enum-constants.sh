#!/bin/bash

# 创建日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# 恢复所有枚举文件到原始状态
log "正在恢复所有枚举文件到原始状态..."
git checkout -f chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/
log "枚举文件恢复完成"

# 修复指定的枚举文件
fix_enum_file() {
    local file=$1
    local enum_name=$(basename "$file" .java)
    log "修复枚举文件: $file"
    
    # 获取文件内容
    local content=$(cat "$file")
    
    # 检查是否是枚举文件
    if ! grep -q "public enum" "$file"; then
        log "不是枚举文件，跳过: $file"
        return
    fi
    
    # 备份原始文件
    cp "$file" "${file}.bak"
    
    # 处理缺少枚举常量的情况
    if grep -q "private.* [a-zA-Z0-9]*;" "$file" && ! grep -q "[A-Z_]*([\"']" "$file"; then
        log "添加枚举常量到 $file"
        
        # 获取枚举的字段
        local fields=$(grep -o "private.* [a-zA-Z0-9]*;" "$file" | awk '{print $2}' | sed 's/;//')
        local field_type=$(grep -o "private.* [a-zA-Z0-9]*;" "$file" | awk '{print $1}' | sed 's/private//')
        
        # 获取构造函数参数
        local constructor_params=$(grep -o "${enum_name}([^)]*)" "$file" | head -1 | sed "s/${enum_name}(//" | sed 's/)//')
        
        # 如果有构造函数但没有枚举常量，添加默认的枚举常量
        local enum_start=$(grep -n "public enum ${enum_name}" "$file" | cut -d':' -f1)
        local new_line=$((enum_start + 1))
        
        # 根据不同类型的字段添加不同的枚举常量
        if [[ "$field_type" == *"String"* ]]; then
            sed -i '' "${new_line}i\\
    UNKNOWN(\"unknown\"),\\
    DEFAULT(\"default\");" "$file"
        elif [[ "$field_type" == *"Integer"* || "$field_type" == *"int"* ]]; then
            sed -i '' "${new_line}i\\
    UNKNOWN(0),\\
    DEFAULT(1);" "$file"
        else
            sed -i '' "${new_line}i\\
    UNKNOWN(\"unknown\"),\\
    DEFAULT(\"default\");" "$file"
        fi
        
        log "成功添加枚举常量到 $file"
    else
        log "枚举文件看起来没问题: $file"
    fi
}

# 修复所有枚举文件
find chat-tag-system-api/src/main/java -name "*.java" -type f -exec grep -l "public enum" {} \; | while read enum_file; do
    fix_enum_file "$enum_file"
done

# 特殊处理 ImageScoreTaskUploadDTO.java
log "修复 ImageScoreTaskUploadDTO.java 文件..."
task_upload_dto="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/dto/task/image_score/ImageScoreTaskUploadDTO.java"
if [ -f "$task_upload_dto" ]; then
    # 检查文件最后是否缺少 }
    if ! grep -q "}$" "$task_upload_dto"; then
        echo "}" >> "$task_upload_dto"
        log "已修复 $task_upload_dto 文件的结束括号"
    fi
fi

log "所有枚举文件修复完成" 