#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

header() {
    echo -e "\n${BLUE}==== $1 ====${NC}\n"
}

header "修复最后一批枚举文件"

# 需要修复的剩余枚举文件
PROBLEM_FILES=(
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/practice/SearchDataCategoryEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/AssistantApplicationSaveModeEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/partner/OnlineStatusEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/TagRuleTypeEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/HttpHeadersEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/ImageTypeEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/application/ApplicationEvaluateEnum.java"
)

FIXED_COUNT=0
FAILED_COUNT=0

for file in "${PROBLEM_FILES[@]}"; do
    if [ -f "$file" ]; then
        info "修复枚举文件: $file"
        # 创建备份
        cp "$file" "${file}.bak"
        
        # 获取包名
        package=$(grep -m 1 "package" "$file" | sed 's/package\s*\(.*\);/\1/')
        
        # 获取类名
        class_name=$(basename "$file" .java)
        
        # 根据文件名创建枚举模板
        case "$class_name" in
            "SearchDataCategoryEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.practice;

/**
 * 搜索数据分类枚举
 * 自动修复生成
 */
public enum SearchDataCategoryEnum {
    ALL(0, "所有"),
    DOCUMENT(1, "文档"),
    IMAGE(2, "图片"),
    VIDEO(3, "视频");

    private final int code;
    private final String desc;

    SearchDataCategoryEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static SearchDataCategoryEnum getByCode(int code) {
        for (SearchDataCategoryEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "AssistantApplicationSaveModeEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 助手应用保存模式枚举
 * 自动修复生成
 */
public enum AssistantApplicationSaveModeEnum {
    DRAFT(0, "草稿"),
    PUBLISHED(1, "已发布"),
    ARCHIVED(2, "已归档");

    private final int code;
    private final String desc;

    AssistantApplicationSaveModeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AssistantApplicationSaveModeEnum getByCode(int code) {
        for (AssistantApplicationSaveModeEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "OnlineStatusEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.partner;

/**
 * 在线状态枚举
 * 自动修复生成
 */
public enum OnlineStatusEnum {
    ONLINE(0, "在线"),
    OFFLINE(1, "离线"),
    AWAY(2, "离开");

    private final int code;
    private final String desc;

    OnlineStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static OnlineStatusEnum getByCode(int code) {
        for (OnlineStatusEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "TagRuleTypeEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 标签规则类型枚举
 * 自动修复生成
 */
public enum TagRuleTypeEnum {
    INCLUDE(0, "包含"),
    EXCLUDE(1, "排除"),
    REGEX(2, "正则表达式");

    private final int code;
    private final String desc;

    TagRuleTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static TagRuleTypeEnum getByCode(int code) {
        for (TagRuleTypeEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "HttpHeadersEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * HTTP头部枚举
 * 自动修复生成
 */
public enum HttpHeadersEnum {
    CONTENT_TYPE("Content-Type"),
    ACCEPT("Accept"),
    AUTHORIZATION("Authorization"),
    USER_AGENT("User-Agent");

    private final String value;

    HttpHeadersEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
EOF
                ;;
            "ImageTypeEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 图片类型枚举
 * 自动修复生成
 */
public enum ImageTypeEnum {
    JPG(0, "jpg", "image/jpeg"),
    PNG(1, "png", "image/png"),
    GIF(2, "gif", "image/gif"),
    BMP(3, "bmp", "image/bmp"),
    WEBP(4, "webp", "image/webp");

    private final int code;
    private final String extension;
    private final String mimeType;

    ImageTypeEnum(int code, String extension, String mimeType) {
        this.code = code;
        this.extension = extension;
        this.mimeType = mimeType;
    }

    public int getCode() {
        return code;
    }

    public String getExtension() {
        return extension;
    }

    public String getMimeType() {
        return mimeType;
    }

    public static ImageTypeEnum getByCode(int code) {
        for (ImageTypeEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }

    public static ImageTypeEnum getByExtension(String extension) {
        if (extension == null) {
            return null;
        }
        
        String ext = extension.toLowerCase();
        for (ImageTypeEnum value : values()) {
            if (value.extension.equals(ext)) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "ApplicationEvaluateEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.application;

/**
 * 应用评估枚举
 * 自动修复生成
 */
public enum ApplicationEvaluateEnum {
    GOOD(0, "好"),
    MEDIUM(1, "中"),
    BAD(2, "差");

    private final int code;
    private final String desc;

    ApplicationEvaluateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ApplicationEvaluateEnum getByCode(int code) {
        for (ApplicationEvaluateEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            *)
                warn "未知枚举类: $class_name"
                FAILED_COUNT=$((FAILED_COUNT + 1))
                continue
                ;;
        esac
        
        if [ $? -eq 0 ]; then
            FIXED_COUNT=$((FIXED_COUNT + 1))
            info "修复成功: $file"
        else
            FAILED_COUNT=$((FAILED_COUNT + 1))
            error "修复失败: $file"
        fi
    else
        warn "文件不存在: $file"
        FAILED_COUNT=$((FAILED_COUNT + 1))
    fi
done

header "修复总结"
info "成功修复: $FIXED_COUNT 个文件"
if [ $FAILED_COUNT -gt 0 ]; then
    warn "修复失败: $FAILED_COUNT 个文件"
fi

exit 0 