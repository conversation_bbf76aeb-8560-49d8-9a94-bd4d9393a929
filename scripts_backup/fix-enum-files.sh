#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

header() {
    echo -e "\n${BLUE}==== $1 ====${NC}\n"
}

header "修复枚举文件语法错误"

# 创建备份目录
BACKUP_DIR="./enum_backups"
mkdir -p "$BACKUP_DIR"

# 找出所有枚举类文件
find chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums -name "*.java" > enum_files.txt
find chat-tag-system-api/src/main/java/com/ke/chat/tag/api/bean -name "*Enum.java" >> enum_files.txt

info "发现 $(wc -l < enum_files.txt) 个枚举文件"

count=0
# 对每个枚举文件进行处理
while IFS= read -r file; do
    count=$((count+1))
    info "处理第 $count 个文件: $file"
    
    # 创建备份
    cp "$file" "$BACKUP_DIR/$(basename "$file").bak"
    
    # 1. 修复文件格式问题
    # 删除所有非法字符（主要是反斜杠）
    sed -i '' 's/\\//g' "$file"
    
    # 2. 修复枚举格式
    # 将结尾的 ",;" 替换为 ";"
    sed -i '' 's/,;/;/g' "$file"
    
    # 3. 将所有 "business" 字段统一改为 "code"
    sed -i '' 's/private final String business/private final String code/g' "$file"
    
    # 4. 确保类使用正确的注解
    # 先读取文件内容
    content=$(cat "$file")
    
    # 检查是否有@Getter注解
    if ! grep -q "@Getter" "$file"; then
        # 如果没有@Getter，添加它
        sed -i '' 's/@AllArgsConstructor/@Getter\n@AllArgsConstructor/g' "$file"
        if ! grep -q "@AllArgsConstructor" "$file"; then
            # 如果也没有@AllArgsConstructor，在enum之前添加两个注解
            sed -i '' 's/public enum/@Getter\n@AllArgsConstructor\npublic enum/g' "$file"
        fi
    fi
    
    # 5. 确保导入正确的包
    if ! grep -q "import lombok.Getter;" "$file"; then
        # 在第一个import之前添加import lombok.Getter;
        sed -i '' '1,/^import/ s/^import/import lombok.Getter;\nimport/' "$file"
    fi
    
    if ! grep -q "import lombok.AllArgsConstructor;" "$file"; then
        # 在第一个import之前添加import lombok.AllArgsConstructor;
        sed -i '' '1,/^import/ s/^import/import lombok.AllArgsConstructor;\nimport/' "$file"
    fi
    
    # 6. 检查并修复UNKNOWN和DEFAULT的位置（应该在最后）
    # 这个比较复杂，需要正则表达式处理，暂时跳过
    
    # 7. 检查字段定义
    # 确保字段定义为 private final String code; 和 private final String desc;
    if ! grep -q "private final String code;" "$file" && ! grep -q "private final String code " "$file"; then
        # 在文件末尾添加字段定义
        echo -e "\n    private final String code;\n    private final String desc;" >> "$file"
    fi
    
    info "完成修复: $file"
done < enum_files.txt

info "所有枚举文件处理完成！"

# 修复AnswerStatusEnum.java
fix_answer_status_enum() {
    local file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/AnswerStatusEnum.java"
    if [ -f "$file" ]; then
        info "修复 $file"
        cp "$file" "${file}.bak"
        
        # 使用sed修复语法错误
        cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/3/1 11:11 上午
 **/
public enum AnswerStatusEnum {
    SUCCESS(0, "成功"),
    FAILED(1, "失败"),
    NO_REPLY(2, "未回复"),
    CUT(3, "截断"),
    CANCEL(4, "取消"),
    SENSITIVE(5, "包含敏感内容"),
    RESTRICTED(6, "限流"),
    TIMEOUT(7, "超时");

    public final int code;
    public final String message;

    AnswerStatusEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static AnswerStatusEnum getStatusByCode(String code) {
        return Arrays.stream(AnswerStatusEnum.values())
            .filter(i -> String.valueOf(i.code).equals(code))
            .findFirst().orElse(SUCCESS);
    }

    public static boolean isErrorStatus(int status) {
        return SUCCESS.code != status && CANCEL.code != status
            && SENSITIVE.code != status && RESTRICTED.code != status && TIMEOUT.code != status;
    }

    public static int getDisplayStatus(int status) {
        // 敏感与限流仍展示归类失败
        if (SENSITIVE.code == status || RESTRICTED.code == status) {
            return FAILED.code;
        }
        return status;
    }
}
EOF
        info "成功修复 $file"
    else
        error "未找到 $file"
    fi
}

# 修复MessageStatusEnum.java
fix_message_status_enum() {
    local file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/MessageStatusEnum.java"
    if [ -f "$file" ]; then
        info "修复 $file"
        cp "$file" "${file}.bak"
        
        # 分析文件内容
        local content=$(cat "$file")
        
        # 使用sed修复语法错误
        cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/3/1 11:12 上午
 **/
public enum MessageStatusEnum {
    SUCCESS(0, "成功"),
    FAILED(1, "失败"),
    SENDING(2, "发送中"),
    CANCEL(3, "取消");

    public final int code;
    public final String message;

    MessageStatusEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static MessageStatusEnum getStatusByCode(String code) {
        return Arrays.stream(MessageStatusEnum.values())
                .filter(i -> String.valueOf(i.code).equals(code))
                .findFirst().orElse(SUCCESS);
    }

    public static boolean isSuccess(int code) {
        return SUCCESS.code == code;
    }
    
    public static boolean isFailed(int code) {
        return FAILED.code == code;
    }
}
EOF
        info "成功修复 $file"
    else
        error "未找到 $file"
    fi
}

# 修复ConversationStatusEnum.java
fix_conversation_status_enum() {
    local file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/ConversationStatusEnum.java"
    if [ -f "$file" ]; then
        info "修复 $file"
        cp "$file" "${file}.bak"
        
        # 使用sed修复语法错误
        cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 **/
public enum ConversationStatusEnum {
    OPEN(0, "开启"),
    CLOSED(1, "关闭");

    public final int code;
    public final String message;

    ConversationStatusEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ConversationStatusEnum getByCode(int code) {
        return Arrays.stream(ConversationStatusEnum.values())
                .filter(i -> i.code == code)
                .findFirst().orElse(OPEN);
    }
}
EOF
        info "成功修复 $file"
    else
        error "未找到 $file"
    fi
}

# 修复MessageTypeEnum.java
fix_message_type_enum() {
    local file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/MessageTypeEnum.java"
    if [ -f "$file" ]; then
        info "修复 $file"
        cp "$file" "${file}.bak"
        
        # 使用sed修复语法错误
        cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * <AUTHOR>
 **/
public enum MessageTypeEnum {
    QUESTION(0, "问题"),
    ANSWER(1, "回答");

    public final int code;
    public final String message;

    MessageTypeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
EOF
        info "成功修复 $file"
    else
        error "未找到 $file"
    fi
}

# 修复PermissionNodeEnum.java
fix_permission_node_enum() {
    local file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/PermissionNodeEnum.java"
    if [ -f "$file" ]; then
        info "修复 $file"
        cp "$file" "${file}.bak"
        
        # 使用sed修复语法错误
        cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * <AUTHOR>
 **/
public enum PermissionNodeEnum {
    OWNER(0, "拥有者"),
    ADMIN(1, "管理员"),
    MEMBER(2, "成员");

    public final int code;
    public final String message;

    PermissionNodeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
EOF
        info "成功修复 $file"
    else
        error "未找到 $file"
    fi
}

# 修复ShareTaskTypeEnum.java
fix_share_task_type_enum() {
    local file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/ShareTaskTypeEnum.java"
    if [ -f "$file" ]; then
        info "修复 $file"
        cp "$file" "${file}.bak"
        
        # 使用sed修复语法错误
        cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/3/10 6:22 下午
 **/
public enum ShareTaskTypeEnum {
    CONVERSATION(0, "会话"),
    SPACE(1, "知识库");

    public final int code;
    public final String message;

    ShareTaskTypeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ShareTaskTypeEnum getByCode(int code) {
        return Arrays.stream(ShareTaskTypeEnum.values())
                .filter(i -> i.code == code)
                .findFirst().orElse(null);
    }
}
EOF
        info "成功修复 $file"
    else
        error "未找到 $file"
    fi
}

# 修复AiExamCreateType.java
fix_ai_exam_create_type() {
    local file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/exam/AiExamCreateType.java"
    if [ -f "$file" ]; then
        info "修复 $file"
        cp "$file" "${file}.bak"
        
        # 使用sed修复语法错误
        cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.exam;

/**
 * <AUTHOR>
 **/
public enum AiExamCreateType {
    MANUAL(0, "手动创建"),
    AUTO(1, "自动创建"),
    IMPORT(2, "导入");

    public final int code;
    public final String message;

    AiExamCreateType(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
EOF
        info "成功修复 $file"
    else
        error "未找到 $file"
    fi
}

# 修复其他枚举文件
fix_audio_response_status_enum() {
    local file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/audio/ResponseStatusEnum.java"
    if [ -f "$file" ]; then
        info "修复 $file"
        cp "$file" "${file}.bak"
        
        # 使用sed修复语法错误
        cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.audio;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 回答结果状态枚举
 * @date 2023/8/8 17:51
 */
@Getter
@AllArgsConstructor
public enum ResponseStatusEnum {
    SUCCESS(0, "成功"),
    PROCESSING(1, "处理中"),
    FINISHED(2, "结束"),
    FAILED(3, "失败");

    private final Integer code;
    private final String desc;
}
EOF
        info "成功修复 $file"
    else
        error "未找到 $file"
    fi
}

# 修复BellaContentEnum.java
fix_bella_content_enum() {
    local file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/application/BellaContentEnum.java"
    if [ -f "$file" ]; then
        info "修复 $file"
        cp "$file" "${file}.bak"
        
        # 使用sed修复语法错误
        cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.application;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * bella内容枚举
 *
 * @author: zhiwei34
 * @date: 2023/9/24
 */
@Getter
@AllArgsConstructor
public enum BellaContentEnum {
    EMPTY(0, "空"),
    TEXT(1, "文本"),
    IMAGE(2, "图片");

    private final Integer code;
    private final String desc;
}
EOF
        info "成功修复 $file"
    else
        error "未找到 $file"
    fi
}

# 执行修复
fix_answer_status_enum
fix_message_status_enum
fix_conversation_status_enum
fix_message_type_enum
fix_permission_node_enum
fix_share_task_type_enum
fix_ai_exam_create_type
fix_audio_response_status_enum
fix_bella_content_enum

# 尝试修复其他可能有问题的枚举文件
info "查找和修复其他枚举文件..."
for enum_file in $(find_enum_files); do
    if ! grep -q "public enum" "$enum_file" || grep -q "private final" "$enum_file"; then
        file_basename=$(basename "$enum_file")
        if [[ "$file_basename" != "AnswerStatusEnum.java" && 
              "$file_basename" != "MessageStatusEnum.java" && 
              "$file_basename" != "ConversationStatusEnum.java" && 
              "$file_basename" != "MessageTypeEnum.java" && 
              "$file_basename" != "PermissionNodeEnum.java" && 
              "$file_basename" != "ShareTaskTypeEnum.java" && 
              "$file_basename" != "AiExamCreateType.java" && 
              "$file_basename" != "ResponseStatusEnum.java" && 
              "$file_basename" != "BellaContentEnum.java" ]]; then
            info "发现需要修复的文件: $enum_file"
            # 这里可以添加其他枚举文件的修复逻辑
        fi
    fi
done

header "修复完成"
info "已修复枚举文件，现在您可以尝试重新构建项目" 