#!/bin/bash

# 创建日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# 1. 恢复所有枚举文件到原始状态
fix_enums() {
    log "正在恢复所有枚举文件到原始状态..."
    git checkout -f chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/
    git checkout -f chat-tag-system-api/src/main/java/com/ke/chat/tag/api/bean/
    git checkout -f chat-tag-system-api/src/main/java/com/ke/chat/tag/api/exception/
    log "枚举文件恢复完成"
}

# 2. 添加必要的Lombok注解到所有DTO类
add_lombok_annotations() {
    log "正在添加Lombok注解到DTO类..."
    find chat-tag-system-api/src/main/java -name "*.java" -type f -exec grep -l "class.*DTO" {} \; | while read file; do
        if ! grep -q "@Data" "$file" && ! grep -q "@Getter" "$file"; then
            # 在package声明下面添加Lombok注解
            sed -i '' -e '/^package/a\\
import lombok.Data;\
import lombok.Getter;\
import lombok.Setter;\
\
@Data' "$file"
            log "已添加Lombok注解到 $file"
        fi
    done
    log "Lombok注解添加完成"
}

# 3. 添加必要的Logger字段到工具类
add_logger_fields() {
    log "正在添加Logger字段到工具类..."
    find chat-tag-system-api/src/main/java -name "*Util.java" -type f | while read file; do
        if ! grep -q "LOGGER" "$file" && ! grep -q "log" "$file"; then
            # 在class定义前添加Logger字段
            sed -i '' -e '/^public class/i\\
import org.slf4j.Logger;\
import org.slf4j.LoggerFactory;\
\
' "$file"
            # 在class定义后添加LOGGER字段
            sed -i '' -e '/^public class/a\\
    private static final Logger LOGGER = LoggerFactory.getLogger('"$(basename "$file" .java)"'.class);' "$file"
            log "已添加Logger字段到 $file"
        fi
    done
    log "Logger字段添加完成"
}

# 4. 修复任务DTO字段重复的问题
fix_duplicate_fields() {
    log "正在修复任务DTO字段重复问题..."
    task_upload_dto="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/dto/task/image_score/ImageScoreTaskUploadDTO.java"
    if [ -f "$task_upload_dto" ]; then
        # 移除重复的taskNo字段
        sed -i '' -e '/private String taskNo;/d' "$task_upload_dto"
        log "已修复 $task_upload_dto 中的重复字段"
    fi
    log "字段重复问题修复完成"
}

# 5. 修复枚举构造函数问题
fix_enum_constructors() {
    log "正在修复枚举构造函数问题..."
    find chat-tag-system-api/src/main/java -name "*.java" -type f -exec grep -l "public enum" {} \; | while read enum_file; do
        # 获取枚举类名
        enum_name=$(grep -o "public enum [A-Za-z0-9_]*" "$enum_file" | sed 's/public enum //')
        
        # 检查是否存在无参构造函数的调用问题
        if grep -q "${enum_name}([^)]*)" "$enum_file" && ! grep -q "${enum_name}() {" "$enum_file"; then
            # 查找枚举的字段
            fields=$(grep -o "private [A-Za-z]* [a-zA-Z]*;" "$enum_file" | awk '{print $3}' | sed 's/;//')
            
            # 检查构造函数
            constructor_params=$(grep -o "${enum_name}([^)]*)" "$enum_file" | head -1 | sed "s/${enum_name}(//" | sed 's/)//')
            
            # 添加默认的构造函数
            if [ -n "$constructor_params" ] && [ -n "$fields" ]; then
                sed -i '' -e "/public enum ${enum_name}/a\\
    private String code;\
    private String desc;\
\
    ${enum_name}(String code, String desc) {\
        this.code = code;\
        this.desc = desc;\
    }\
\
    public String getCode() {\
        return code;\
    }\
\
    public String getDesc() {\
        return desc;\
    }" "$enum_file"
                log "已修复枚举构造函数: $enum_file"
            fi
        fi
    done
    log "枚举构造函数修复完成"
}

# 6. 修复ErrorCode类
fix_error_code() {
    log "正在修复ErrorCode类..."
    error_code_file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/exception/ErrorCode.java"
    if [ -f "$error_code_file" ]; then
        cat > "$error_code_file" << 'EOF'
package com.ke.chat.tag.api.exception;
import com.ke.boot.common.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;

public enum ErrorCode {
    /**
     * 系统异常
     */
    SYSTEM_ERROR("system_error", "系统异常"),
    /**
     * 参数校验失败
     */
    PARAM_VALIDATE_FAILED("param_validate_failed", "参数校验失败"),
    /**
     * 业务异常
     */
    BUSINESS_ERROR("business_error", "业务异常"),
    /**
     * 无效参数
     */
    INVALID_ARGUMENT("invalid_argument", "无效参数");

    private String code;
    private String defaultMsg;

    ErrorCode(String code, String defaultMsg) {
        this.code = code;
        this.defaultMsg = defaultMsg;
    }

    public String getCode() {
        return code;
    }

    public String getDefaultMsg() {
        return defaultMsg;
    }

    public BusinessException exception() {
        return new BusinessException(code, defaultMsg);
    }

    public BusinessException exception(String message) {
        if (StringUtils.isBlank(message)) {
            return exception();
        }
        return new BusinessException(code, message);
    }
}
EOF
        log "ErrorCode类已修复"
    fi
}

# 7. 修复MessageTypeEnum类
fix_message_type_enum() {
    log "正在修复MessageTypeEnum类..."
    message_type_file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/bean/assistant/MessageTypeEnum.java"
    if [ -f "$message_type_file" ]; then
        cat > "$message_type_file" << 'EOF'
package com.ke.chat.tag.api.bean.assistant;

public enum MessageTypeEnum {
    /**
     * 文本消息
     */
    TEXT("text"),
    /**
     * 图片消息
     */
    IMAGE("image"),
    /**
     * 文件消息
     */
    FILE("file");

    private String code;
    private String type;

    MessageTypeEnum(String code) {
        this.code = code;
        this.type = code;
    }

    public String getCode() {
        return code;
    }

    public String getType() {
        return type;
    }
}
EOF
        log "MessageTypeEnum类已修复"
    fi
}

# 执行所有修复步骤
main() {
    log "开始执行项目修复脚本..."
    
    fix_enums
    add_lombok_annotations
    add_logger_fields
    fix_duplicate_fields
    fix_enum_constructors
    fix_error_code
    fix_message_type_enum
    
    log "项目修复脚本执行完成"
}

# 运行主函数
main 