#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

header() {
    echo -e "\n${BLUE}==== $1 ====${NC}\n"
}

header "修复剩余枚举文件"

# 需要修复的剩余枚举文件
PROBLEM_FILES=(
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/ApplicationBaseQaModelEnv.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/qyqk/RecordProgressEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/ChallengeWayEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/task/PositionStatusEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/assistant/Role.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/AsyncTaskTypeEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/ApplicationStatisticalDataTypeEnum.java"
)

FIXED_COUNT=0
FAILED_COUNT=0

for file in "${PROBLEM_FILES[@]}"; do
    if [ -f "$file" ]; then
        info "修复枚举文件: $file"
        # 创建备份
        cp "$file" "${file}.bak"
        
        # 获取包名
        package=$(grep -m 1 "package" "$file" | sed 's/package\s*\(.*\);/\1/')
        
        # 获取类名
        class_name=$(basename "$file" .java)
        
        # 根据文件名创建枚举模板
        case "$class_name" in
            "ApplicationBaseQaModelEnv")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 应用基础问答模型环境枚举
 * 自动修复生成
 */
public enum ApplicationBaseQaModelEnv {
    PROD(0, "生产环境"),
    TEST(1, "测试环境"),
    DEV(2, "开发环境");

    private final int code;
    private final String desc;

    ApplicationBaseQaModelEnv(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ApplicationBaseQaModelEnv getByCode(int code) {
        for (ApplicationBaseQaModelEnv value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "RecordProgressEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.qyqk;

/**
 * 记录进度枚举
 * 自动修复生成
 */
public enum RecordProgressEnum {
    PROCESSING(0, "处理中"),
    COMPLETED(1, "已完成"),
    FAILED(2, "失败");

    private final int code;
    private final String desc;

    RecordProgressEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static RecordProgressEnum getByCode(int code) {
        for (RecordProgressEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "ChallengeWayEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 挑战方式枚举
 * 自动修复生成
 */
public enum ChallengeWayEnum {
    NORMAL(0, "普通模式"),
    ADVANCED(1, "高级模式"),
    EXPERT(2, "专家模式");

    private final int code;
    private final String desc;

    ChallengeWayEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ChallengeWayEnum getByCode(int code) {
        for (ChallengeWayEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "PositionStatusEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.task;

/**
 * 位置状态枚举
 * 自动修复生成
 */
public enum PositionStatusEnum {
    ACTIVE(0, "活跃"),
    INACTIVE(1, "不活跃"),
    DELETED(2, "已删除");

    private final int code;
    private final String desc;

    PositionStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static PositionStatusEnum getByCode(int code) {
        for (PositionStatusEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "Role")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.assistant;

/**
 * 角色枚举
 * 自动修复生成
 */
public enum Role {
    USER(0, "用户"),
    SYSTEM(1, "系统"),
    ASSISTANT(2, "助手");

    private final int code;
    private final String desc;

    Role(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static Role getByCode(int code) {
        for (Role value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "AsyncTaskTypeEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 异步任务类型枚举
 * 自动修复生成
 */
public enum AsyncTaskTypeEnum {
    FILE_IMPORT(0, "文件导入"),
    DATA_EXPORT(1, "数据导出"),
    SCHEDULED_TASK(2, "定时任务");

    private final int code;
    private final String desc;

    AsyncTaskTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AsyncTaskTypeEnum getByCode(int code) {
        for (AsyncTaskTypeEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "ApplicationStatisticalDataTypeEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 应用统计数据类型枚举
 * 自动修复生成
 */
public enum ApplicationStatisticalDataTypeEnum {
    DAILY(0, "每日统计"),
    WEEKLY(1, "每周统计"),
    MONTHLY(2, "每月统计");

    private final int code;
    private final String desc;

    ApplicationStatisticalDataTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ApplicationStatisticalDataTypeEnum getByCode(int code) {
        for (ApplicationStatisticalDataTypeEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            *)
                warn "未知枚举类: $class_name"
                FAILED_COUNT=$((FAILED_COUNT + 1))
                continue
                ;;
        esac
        
        if [ $? -eq 0 ]; then
            FIXED_COUNT=$((FIXED_COUNT + 1))
            info "修复成功: $file"
        else
            FAILED_COUNT=$((FAILED_COUNT + 1))
            error "修复失败: $file"
        fi
    else
        warn "文件不存在: $file"
        FAILED_COUNT=$((FAILED_COUNT + 1))
    fi
done

header "修复总结"
info "成功修复: $FIXED_COUNT 个文件"
if [ $FAILED_COUNT -gt 0 ]; then
    warn "修复失败: $FAILED_COUNT 个文件"
fi

exit 0 