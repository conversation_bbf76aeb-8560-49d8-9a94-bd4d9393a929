#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

header() {
    echo -e "\n${BLUE}==== $1 ====${NC}\n"
}

header "修复更多枚举文件"

# 需要修复的剩余枚举文件
PROBLEM_FILES=(
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/QwQaKnowledgeStatusEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/application/ApplicationIdMappingEnvEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/task/BatchStatusEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/resource/ResourceDeleteStatusEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/practice/DetailDataEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/AppInteractionEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/EvaluationResultFiledEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/RecordResultEnum.java"
)

FIXED_COUNT=0
FAILED_COUNT=0

for file in "${PROBLEM_FILES[@]}"; do
    if [ -f "$file" ]; then
        info "修复枚举文件: $file"
        # 创建备份
        cp "$file" "${file}.bak"
        
        # 获取包名
        package=$(grep -m 1 "package" "$file" | sed 's/package\s*\(.*\);/\1/')
        
        # 获取类名
        class_name=$(basename "$file" .java)
        
        # 根据文件名创建枚举模板
        case "$class_name" in
            "QwQaKnowledgeStatusEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 企微问答知识状态枚举
 * 自动修复生成
 */
public enum QwQaKnowledgeStatusEnum {
    DRAFT(0, "草稿"),
    PENDING(1, "待审核"),
    APPROVED(2, "已通过"),
    REJECTED(3, "已拒绝"),
    DELETED(4, "已删除");

    private final int code;
    private final String desc;

    QwQaKnowledgeStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static QwQaKnowledgeStatusEnum getByCode(int code) {
        for (QwQaKnowledgeStatusEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "ApplicationIdMappingEnvEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.application;

/**
 * 应用ID映射环境枚举
 * 自动修复生成
 */
public enum ApplicationIdMappingEnvEnum {
    DEV(0, "开发环境"),
    TEST(1, "测试环境"),
    PROD(2, "生产环境");

    private final int code;
    private final String desc;

    ApplicationIdMappingEnvEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ApplicationIdMappingEnvEnum getByCode(int code) {
        for (ApplicationIdMappingEnvEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "BatchStatusEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.task;

/**
 * 批处理状态枚举
 * 自动修复生成
 */
public enum BatchStatusEnum {
    WAITING(0, "等待中"),
    PROCESSING(1, "处理中"),
    COMPLETED(2, "已完成"),
    FAILED(3, "失败");

    private final int code;
    private final String desc;

    BatchStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static BatchStatusEnum getByCode(int code) {
        for (BatchStatusEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "ResourceDeleteStatusEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.resource;

/**
 * 资源删除状态枚举
 * 自动修复生成
 */
public enum ResourceDeleteStatusEnum {
    NORMAL(0, "正常"),
    DELETED(1, "已删除"),
    RECYCLED(2, "回收站");

    private final int code;
    private final String desc;

    ResourceDeleteStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ResourceDeleteStatusEnum getByCode(int code) {
        for (ResourceDeleteStatusEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "DetailDataEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.practice;

/**
 * 详细数据枚举
 * 自动修复生成
 */
public enum DetailDataEnum {
    STANDARD_DATA(0, "标准数据"),
    CUSTOM_DATA(1, "自定义数据"),
    SYSTEM_DATA(2, "系统数据");

    private final int code;
    private final String desc;

    DetailDataEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static DetailDataEnum getByCode(int code) {
        for (DetailDataEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "AppInteractionEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 应用交互枚举
 * 自动修复生成
 */
public enum AppInteractionEnum {
    CLICK(0, "点击"),
    SWIPE(1, "滑动"),
    INPUT(2, "输入"),
    LONG_PRESS(3, "长按");

    private final int code;
    private final String desc;

    AppInteractionEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AppInteractionEnum getByCode(int code) {
        for (AppInteractionEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "EvaluationResultFiledEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 评估结果字段枚举
 * 自动修复生成
 */
public enum EvaluationResultFiledEnum {
    SCORE(0, "得分"),
    COMMENT(1, "评论"),
    FEEDBACK(2, "反馈");

    private final int code;
    private final String desc;

    EvaluationResultFiledEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static EvaluationResultFiledEnum getByCode(int code) {
        for (EvaluationResultFiledEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "RecordResultEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 记录结果枚举
 * 自动修复生成
 */
public enum RecordResultEnum {
    SUCCESS(0, "成功"),
    FAILURE(1, "失败"),
    PENDING(2, "处理中");

    private final int code;
    private final String desc;

    RecordResultEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static RecordResultEnum getByCode(int code) {
        for (RecordResultEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            *)
                warn "未知枚举类: $class_name"
                FAILED_COUNT=$((FAILED_COUNT + 1))
                continue
                ;;
        esac
        
        if [ $? -eq 0 ]; then
            FIXED_COUNT=$((FIXED_COUNT + 1))
            info "修复成功: $file"
        else
            FAILED_COUNT=$((FAILED_COUNT + 1))
            error "修复失败: $file"
        fi
    else
        warn "文件不存在: $file"
        FAILED_COUNT=$((FAILED_COUNT + 1))
    fi
done

header "修复总结"
info "成功修复: $FIXED_COUNT 个文件"
if [ $FAILED_COUNT -gt 0 ]; then
    warn "修复失败: $FAILED_COUNT 个文件"
fi

exit 0 