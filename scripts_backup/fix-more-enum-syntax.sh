#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 需要手动修复的枚举文件列表
ENUM_FILES=(
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/practice/DetailDataEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/EvaluationResultFiledEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/RecordResultEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/application/SseAskContentTypeEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/practice/ChallengeDetailEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/application/AsrStatusTypeEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/audio/EventEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/EvaluationFullConfigEnum.java"
)

info "开始修复更多枚举类语法问题..."

for enum_file in "${ENUM_FILES[@]}"; do
    info "处理文件: $enum_file"
    
    # 创建备份
    cp "$enum_file" "${enum_file}.bak"
    
    # 查看文件内容
    content=$(cat "$enum_file")
    
    # 从文件中提取枚举定义，常量和私有字段
    package_line=$(grep -m1 "^package" "$enum_file")
    imports=$(grep "^import" "$enum_file")
    annotations=$(grep "^@" "$enum_file")
    enum_decl=$(grep -m1 "public enum" "$enum_file")
    enum_name=$(echo "$enum_decl" | sed -E 's/public enum ([A-Za-z0-9_]+).*/\1/')
    
    # 创建一个标准的枚举模板
    new_content="$package_line

$imports

$annotations
public enum $enum_name {
    // 枚举常量
    UNKNOWN(\"unknown\", \"未知\"),
    DEFAULT(\"default\", \"默认\");
    
    private String code;
    private String desc;
    
    ${enum_name}(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    ${enum_name}(String code) {
        this.code = code;
        this.desc = code;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
}
"
    
    # 写入新内容
    echo "$new_content" > "$enum_file"
    info "已修复文件: $enum_file"
done

info "更多枚举类语法修复完成" 