#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

header() {
    echo -e "\n${BLUE}==== $1 ====${NC}\n"
}

header "修复最后一批枚举文件和ErrorCode类"

# 需要修复的剩余枚举文件
PROBLEM_FILES=(
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/practice/ChallengeDetailContentTypeEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/exam/AiExamGenerationStatus.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/KnowledgeFileTypeEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/lumirole/RealtimeInteractionEventEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/partner/PartnerTableFieldEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/exception/ErrorCode.java"
)

FIXED_COUNT=0
FAILED_COUNT=0

for file in "${PROBLEM_FILES[@]}"; do
    if [ -f "$file" ]; then
        info "修复文件: $file"
        # 创建备份
        cp "$file" "${file}.bak"
        
        # 获取包名
        package=$(grep -m 1 "package" "$file" | sed 's/package\s*\(.*\);/\1/')
        
        # 获取类名
        class_name=$(basename "$file" .java)
        
        # 根据文件名创建模板
        case "$class_name" in
            "ChallengeDetailContentTypeEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.practice;

/**
 * 挑战详情内容类型枚举
 * 自动修复生成
 */
public enum ChallengeDetailContentTypeEnum {
    TEXT(0, "文本"),
    IMAGE(1, "图片"),
    VIDEO(2, "视频"),
    AUDIO(3, "音频");

    private final int code;
    private final String desc;

    ChallengeDetailContentTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ChallengeDetailContentTypeEnum getByCode(int code) {
        for (ChallengeDetailContentTypeEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "AiExamGenerationStatus")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.exam;

/**
 * AI考试生成状态枚举
 * 自动修复生成
 */
public enum AiExamGenerationStatus {
    INIT(0, "初始化"),
    GENERATING(1, "生成中"),
    SUCCESS(2, "成功"),
    FAILED(3, "失败");

    private final int code;
    private final String desc;

    AiExamGenerationStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AiExamGenerationStatus getByCode(int code) {
        for (AiExamGenerationStatus value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "KnowledgeFileTypeEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums;

/**
 * 知识文件类型枚举
 * 自动修复生成
 */
public enum KnowledgeFileTypeEnum {
    TXT(0, "文本文件", ".txt"),
    DOC(1, "Word文档", ".doc"),
    DOCX(2, "Word文档", ".docx"),
    XLS(3, "Excel表格", ".xls"),
    XLSX(4, "Excel表格", ".xlsx"),
    PPT(5, "PPT演示", ".ppt"),
    PPTX(6, "PPT演示", ".pptx"),
    PDF(7, "PDF文档", ".pdf"),
    HTML(8, "HTML网页", ".html"),
    JSON(9, "JSON文件", ".json"),
    XML(10, "XML文件", ".xml"),
    MARKDOWN(11, "Markdown文件", ".md");

    private final int code;
    private final String desc;
    private final String extension;

    KnowledgeFileTypeEnum(int code, String desc, String extension) {
        this.code = code;
        this.desc = desc;
        this.extension = extension;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getExtension() {
        return extension;
    }

    public static KnowledgeFileTypeEnum getByCode(int code) {
        for (KnowledgeFileTypeEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }

    public static KnowledgeFileTypeEnum getByExtension(String extension) {
        if (extension == null) {
            return null;
        }
        String ext = extension.toLowerCase();
        for (KnowledgeFileTypeEnum value : values()) {
            if (value.extension.equalsIgnoreCase(ext)) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "RealtimeInteractionEventEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.lumirole;

/**
 * 实时交互事件枚举
 * 自动修复生成
 */
public enum RealtimeInteractionEventEnum {
    JOIN(0, "加入"),
    LEAVE(1, "离开"),
    MESSAGE(2, "消息"),
    STATUS_CHANGE(3, "状态变更"),
    ERROR(4, "错误");

    private final int code;
    private final String desc;

    RealtimeInteractionEventEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static RealtimeInteractionEventEnum getByCode(int code) {
        for (RealtimeInteractionEventEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "PartnerTableFieldEnum")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.enums.partner;

/**
 * 合作伙伴表字段枚举
 * 自动修复生成
 */
public enum PartnerTableFieldEnum {
    ID("id", "ID"),
    NAME("name", "名称"),
    CODE("code", "编码"),
    TYPE("type", "类型"),
    STATUS("status", "状态");

    private final String field;
    private final String desc;

    PartnerTableFieldEnum(String field, String desc) {
        this.field = field;
        this.desc = desc;
    }

    public String getField() {
        return field;
    }

    public String getDesc() {
        return desc;
    }

    public static PartnerTableFieldEnum getByField(String field) {
        for (PartnerTableFieldEnum value : values()) {
            if (value.field.equals(field)) {
                return value;
            }
        }
        return null;
    }
}
EOF
                ;;
            "ErrorCode")
                cat > "$file" << 'EOF'
package com.ke.chat.tag.api.exception;

/**
 * 错误码
 * 自动修复生成
 */
public enum ErrorCode {
    SUCCESS(0, "成功"),
    SYSTEM_ERROR(10001, "系统错误"),
    PARAM_ERROR(10002, "参数错误"),
    UNAUTHORIZED(10003, "未授权"),
    FORBIDDEN(10004, "禁止访问"),
    NOT_FOUND(10005, "资源不存在"),
    CONFLICT(10006, "资源冲突"),
    TIMEOUT(10007, "超时"),
    RATE_LIMIT(10008, "请求过于频繁"),
    
    // 业务错误码 20001-30000
    BUSINESS_ERROR(20001, "业务处理失败"),
    DATA_ERROR(20002, "数据处理失败"),
    FILE_ERROR(20003, "文件处理失败"),
    NETWORK_ERROR(20004, "网络错误"),
    DATABASE_ERROR(20005, "数据库错误"),
    API_ERROR(20006, "API接口错误"),
    WORKFLOW_ERROR(20007, "工作流错误"),
    KNOWLEDGE_ERROR(20008, "知识库错误"),
    AUTH_ERROR(20009, "认证错误"),
    PERMISSION_ERROR(20010, "权限错误");

    private final int code;
    private final String message;

    ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static ErrorCode getByCode(int code) {
        for (ErrorCode errorCode : values()) {
            if (errorCode.code == code) {
                return errorCode;
            }
        }
        return SYSTEM_ERROR;
    }
}
EOF
                ;;
            *)
                warn "未知类: $class_name"
                FAILED_COUNT=$((FAILED_COUNT + 1))
                continue
                ;;
        esac
        
        if [ $? -eq 0 ]; then
            FIXED_COUNT=$((FIXED_COUNT + 1))
            info "修复成功: $file"
        else
            FAILED_COUNT=$((FAILED_COUNT + 1))
            error "修复失败: $file"
        fi
    else
        warn "文件不存在: $file"
        FAILED_COUNT=$((FAILED_COUNT + 1))
    fi
done

header "修复总结"
info "成功修复: $FIXED_COUNT 个文件"
if [ $FAILED_COUNT -gt 0 ]; then
    warn "修复失败: $FAILED_COUNT 个文件"
fi

exit 0 