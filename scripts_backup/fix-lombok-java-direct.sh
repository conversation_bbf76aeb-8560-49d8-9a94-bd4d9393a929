#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

info "=== Lombok与Java直接兼容性修复工具 ==="

# 检查Java版本
JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | awk -F. '{print $1}')
info "检测到Java版本: $JAVA_VERSION"

# 必要的JVM参数 - 无论Java版本如何都使用，以确保兼容性
JVM_ARGS="--add-opens=jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED"

# Maven参数
MAVEN_ARGS="-Dmaven.resolver.transport.http.blocker.disabled=true -Dmaven.wagon.http.ssl.insecure=true -Dmaven.wagon.http.ssl.allowall=true -DremoteRepositories=http://maven.lianjia.com/content/groups/Lianjia-Group -DaltDeploymentRepository=lianjia-group::default::http://maven.lianjia.com/content/groups/Lianjia-Group"

# 信息提示
info "将使用以下JVM参数运行Maven:"
echo "$JVM_ARGS"
info "将使用以下Maven参数:"
echo "$MAVEN_ARGS"

# 清理可能存在问题的缓存文件
info "是否清除可能有问题的缓存依赖?"
read -p "清除缓存可解决某些依赖问题 (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    info "清除缓存..."
    # 备份当前缓存目录
    if [ -d "$HOME/.m2/repository/com/lianjia" ]; then
        BACKUP_DIR="$HOME/.m2/repository/com/lianjia.bak.$(date +%Y%m%d%H%M%S)"
        mkdir -p $(dirname "$BACKUP_DIR")
        mv "$HOME/.m2/repository/com/lianjia" "$BACKUP_DIR" 2>/dev/null
        info "已备份链家依赖到: $BACKUP_DIR"
    fi
    
    if [ -d "$HOME/.m2/repository/com/ke" ]; then
        BACKUP_DIR="$HOME/.m2/repository/com/ke.bak.$(date +%Y%m%d%H%M%S)"
        mkdir -p $(dirname "$BACKUP_DIR")
        mv "$HOME/.m2/repository/com/ke" "$BACKUP_DIR" 2>/dev/null
        info "已备份贝壳依赖到: $BACKUP_DIR"
    fi
    
    # 强制删除任何剩余的问题依赖
    rm -rf "$HOME/.m2/repository/com/lianjia" 2>/dev/null
    rm -rf "$HOME/.m2/repository/com/ke" 2>/dev/null
    
    info "缓存已清除"
fi

# 运行Maven构建
info "是否使用直接兼容模式运行Maven构建?"
read -p "运行构建? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    info "开始使用直接兼容模式构建..."
    
    # 完整构建命令
    BUILD_CMD="MAVEN_OPTS=\"$JVM_ARGS\" mvn clean package -DskipTests $MAVEN_ARGS -U"
    
    info "执行命令: $BUILD_CMD"
    # 执行命令
    eval $BUILD_CMD
    
    # 检查构建结果
    if [ $? -eq 0 ]; then
        info "构建成功!"
        
        # 查找构建产物
        JAR_FILE=$(find "./chat-tag-system-start/target" -name "*.jar" | grep -v "sources" | grep -v "javadoc" | head -1)
        
        if [ -n "$JAR_FILE" ]; then
            info "找到JAR包: $JAR_FILE"
            
            # 询问是否运行
            read -p "是否运行应用? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                # 运行参数
                JAVA_OPTS="-Xms1024m -Xmx2048m"
                SPRING_ARGS="--spring.profiles.active=passport,test"
                
                # 运行命令
                info "启动应用: java $JAVA_OPTS -jar $JAR_FILE $SPRING_ARGS"
                java $JAVA_OPTS -jar $JAR_FILE $SPRING_ARGS
            else
                info "可以使用以下命令运行应用:"
                echo "java -Xms1024m -Xmx2048m -jar $JAR_FILE --spring.profiles.active=passport,test"
            fi
        else
            warn "未找到JAR包，无法运行"
        fi
    else
        error "构建失败!"
        info "如果构建仍然失败，可以尝试使用工具链:"
        echo "创建toolchains.xml文件:"
        echo "<toolchains>"
        echo "  <toolchain>"
        echo "    <type>jdk</type>"
        echo "    <provides>"
        echo "      <version>11</version>"
        echo "    </provides>"
        echo "    <configuration>"
        echo "      <jdkHome>/path/to/jdk-11</jdkHome>"
        echo "    </configuration>"
        echo "  </toolchain>"
        echo "</toolchains>"
        echo ""
        echo "然后运行: mvn -t toolchains.xml clean package"
    fi
else
    info "已取消构建"
    info "您可以手动运行以下命令进行构建:"
    echo "MAVEN_OPTS=\"$JVM_ARGS\" mvn clean package -DskipTests $MAVEN_ARGS -U"
fi 