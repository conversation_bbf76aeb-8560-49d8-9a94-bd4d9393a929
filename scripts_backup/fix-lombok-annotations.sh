#!/bin/bash

# 创建日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

log "开始添加 Lombok 注解到类..."

# 为工具类添加 Logger 字段
add_logger_to_utils() {
    log "为工具类添加 Logger 字段..."
    
    # 查找所有工具类
    find chat-tag-system-api/src/main/java -name "*Util.java" -type f | while read util_file; do
        log "处理: $util_file"
        
        # 检查是否已有 LOGGER 字段
        if ! grep -q "LOGGER" "$util_file"; then
            # 检查是否已导入 Logger
            if ! grep -q "import org.slf4j.Logger" "$util_file"; then
                sed -i '' '1,/^package/!{/^import/!{/^$/!{/^package/!{/^import/!{/^public class/i\
import org.slf4j.Logger;\
import org.slf4j.LoggerFactory;\
                }}}}}' "$util_file"
            fi
            
            # 添加 LOGGER 字段到类
            sed -i '' '/^public class/a\
    private static final Logger LOGGER = LoggerFactory.getLogger('"$(basename "$util_file" .java)"'.class);' "$util_file"
            
            log "已添加 Logger 字段到 $util_file"
        else
            log "$util_file 已有 Logger 字段，跳过"
        fi
    done
}

# 为枚举类添加必要的字段和方法
fix_enums() {
    log "修复枚举类..."
    
    # 查找所有枚举类
    find chat-tag-system-api/src/main/java -name "*.java" -type f -exec grep -l "public enum" {} \; | while read enum_file; do
        log "处理枚举: $enum_file"
        enum_name=$(basename "$enum_file" .java)
        
        # 检查是否有构造函数但没有对应的字段
        if grep -q "${enum_name}(" "$enum_file" && grep -q "String" "$enum_file"; then
            # 检查是否引用了 code 字段但没有定义
            if grep -q "getCode()" "$enum_file" && ! grep -q "private.*code" "$enum_file"; then
                log "添加 code 字段到 $enum_file"
                sed -i '' '/public enum/a\
    private String code;\
    private String desc;\
\
    '"$enum_name"'(String code, String desc) {\
        this.code = code;\
        this.desc = desc;\
    }\
\
    public String getCode() {\
        return code;\
    }\
\
    public String getDesc() {\
        return desc;\
    }' "$enum_file"
                log "已添加代码和描述字段到 $enum_file"
            fi
            
            # 检查是否引用了 type 字段但没有定义
            if grep -q "getType()" "$enum_file" && ! grep -q "private.*type" "$enum_file"; then
                log "添加 type 字段到 $enum_file"
                sed -i '' '/public enum/a\
    private String type;\
\
    '"$enum_name"'(String type) {\
        this.type = type;\
    }\
\
    public String getType() {\
        return type;\
    }' "$enum_file"
                log "已添加类型字段到 $enum_file"
            fi
        fi
    done
}

# 修复 ErrorCode 类
fix_error_code() {
    log "修复 ErrorCode 类..."
    error_code_file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/exception/ErrorCode.java"
    
    if [ -f "$error_code_file" ]; then
        # 检查 code 字段是否为 private
        if grep -q "private String code;" "$error_code_file"; then
            sed -i '' 's/private String code;/private String code;/g' "$error_code_file"
            log "已修复 ErrorCode 类的访问控制"
        fi
    fi
}

# 修复特定的 DTO 类
fix_dtos() {
    log "修复 DTO 类..."
    
    # 修复 ImageScoreTaskTagInfo 类
    tag_info_file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/dto/task/image_score/ImageScoreTaskTagInfo.java"
    if [ -f "$tag_info_file" ]; then
        # 检查是否已经有 Lombok 注解
        if ! grep -q "@Data" "$tag_info_file"; then
            # 添加 Lombok 注解
            sed -i '' '/^package/a\
import lombok.Data;\
import lombok.Setter;\
import lombok.Getter;\
' "$tag_info_file"
            
            # 添加 @Data 注解到类
            sed -i '' '/^public class/i\
@Data' "$tag_info_file"
            
            log "已添加 Lombok 注解到 ImageScoreTaskTagInfo 类"
        fi
        
        # 检查是否实现了 TaskWithNo 接口的必要方法
        if ! grep -q "setTaskNo" "$tag_info_file"; then
            # 添加字段和方法
            sed -i '' '/^public class/a\
    private String taskNo;\
\
    @Override\
    public void setTaskNo(String taskNo) {\
        this.taskNo = taskNo;\
    }\
\
    @Override\
    public String getTaskNo() {\
        return taskNo;\
    }' "$tag_info_file"
            
            log "已添加 taskNo 字段和方法到 ImageScoreTaskTagInfo 类"
        fi
    fi
    
    # 查找所有可能缺失 Lombok 注解的 DTO 类
    find chat-tag-system-api/src/main/java -name "*DTO.java" -type f | while read dto_file; do
        # 检查是否已经有 Lombok 注解
        if ! grep -q "@Data" "$dto_file" && ! grep -q "@Getter" "$dto_file"; then
            # 添加 Lombok 注解
            sed -i '' '/^package/a\
import lombok.Data;\
' "$dto_file"
            
            # 添加 @Data 注解到类
            sed -i '' '/^public class/i\
@Data' "$dto_file"
            
            log "已添加 Lombok 注解到 $(basename "$dto_file")"
        fi
    done
}

# 修复 BasePageRequest 类
fix_base_page_request() {
    log "修复 BasePageRequest 类..."
    base_page_request_file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/response/BasePageRequest.java"
    
    if [ -f "$base_page_request_file" ]; then
        # 检查是否已经有 Lombok 注解
        if ! grep -q "@Data" "$base_page_request_file"; then
            # 添加 Lombok 注解
            sed -i '' '/^package/a\
import lombok.Data;\
import lombok.NoArgsConstructor;\
import lombok.AllArgsConstructor;\
' "$base_page_request_file"
            
            # 添加 @Data 注解到类
            sed -i '' '/^public class/i\
@Data\
@NoArgsConstructor\
@AllArgsConstructor' "$base_page_request_file"
            
            log "已添加 Lombok 注解到 BasePageRequest 类"
        fi
    fi
}

# 执行修复
add_logger_to_utils
fix_enums
fix_error_code
fix_dtos
fix_base_page_request

log "Lombok 注解添加完成" 