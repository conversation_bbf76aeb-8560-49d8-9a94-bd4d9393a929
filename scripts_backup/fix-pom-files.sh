#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

header() {
    echo -e "\n${BLUE}==== $1 ====${NC}\n"
}

header "修复POM文件"

# 修复根POM文件
ROOT_POM="pom.xml"
if [ -f "$ROOT_POM" ]; then
    info "修复根POM文件"
    cp "$ROOT_POM" "${ROOT_POM}.bak"
    
    # 使用cat和awk编辑POM文件
    awk '
    BEGIN { fix_mode = 0; }
    /<!-- Java 11\+ / { 
        fix_mode = 1; 
        print "        <!-- Java 11+ 兼容性所需依赖 -->";
        print "        <dependency>";
        print "            <groupId>javax.xml.bind</groupId>";
        print "            <artifactId>jaxb-api</artifactId>";
        print "            <version>2.3.1</version>";
        print "        </dependency>";
        next;
    }
    fix_mode == 1 && /<\/dependency>/ { 
        fix_mode = 0;
        next;
    }
    fix_mode == 1 { next; }
    { print }
    ' "${ROOT_POM}.bak" > "$ROOT_POM"
    
    info "根POM文件修复完成"
else
    error "未找到根POM文件"
fi

# 修复API模块POM文件
API_POM="chat-tag-system-api/pom.xml"
if [ -f "$API_POM" ]; then
    info "修复API模块POM文件"
    cp "$API_POM" "${API_POM}.bak"
    
    # 使用cat和awk编辑POM文件
    awk '
    BEGIN { fix_mode = 0; }
    /<!-- Java 9\+ / { 
        fix_mode = 1; 
        print "      <!-- Java 9+ 兼容性所需依赖 -->";
        print "      <dependency>";
        print "          <groupId>javax.activation</groupId>";
        print "          <artifactId>javax.activation-api</artifactId>";
        print "          <version>1.2.0</version>";
        print "      </dependency>";
        next;
    }
    fix_mode == 1 && /<\/dependency>/ { 
        fix_mode = 0;
        next;
    }
    fix_mode == 1 { next; }
    { print }
    ' "${API_POM}.bak" > "$API_POM"
    
    info "API模块POM文件修复完成"
else
    error "未找到API模块POM文件"
fi

header "修复完成"
info "已修复POM文件，现在您可以尝试重新构建项目"
echo "JAVA_HOME=\"/Library/Java/JavaVirtualMachines/adoptopenjdk-11.jdk/Contents/Home\" mvn -t toolchains.xml clean package -DskipTests -Dmaven.resolver.transport.http.blocker.disabled=true -Dmaven.wagon.http.ssl.insecure=true -DremoteRepositories=http://maven.lianjia.com/content/groups/Lianjia-Group -U" 