#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

info "开始全面修复项目问题..."

# 1. 修复工具类中缺少的Logger字段
fix_logger() {
    info "正在修复工具类中缺少的Logger字段..."
    
    # 为工具类添加Logger字段和导入
    UTIL_FILES=$(find chat-tag-system-api/src/main/java/com/ke/chat/tag/api/util -name "*.java")
    
    for file in $UTIL_FILES; do
        if grep -q "Logger" "$file"; then
            info "文件 $file 已包含Logger字段"
        else
            info "为文件 $file 添加Logger字段"
            # 创建临时文件
            temp_file=$(mktemp)
            
            # 提取包名
            package_line=$(grep -m1 "^package" "$file")
            
            # 创建新内容，添加Logger导入和字段
            cat > "$temp_file" << EOF
$package_line

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

$(grep -v "^package" "$file" | sed '/^public class/a \    private static final Logger LOGGER = LoggerFactory.getLogger('$(basename "$file" .java)'.class);')
EOF
            
            # 将临时文件内容写回原文件
            mv "$temp_file" "$file"
        fi
    done
}

# 2. 修复所有枚举类问题
fix_enums() {
    info "正在修复所有枚举类问题..."
    
    # 查找所有枚举类
    ENUM_FILES=$(find chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums -name "*.java")
    
    for file in $ENUM_FILES; do
        info "正在处理枚举类: $file"
        
        # 创建备份
        cp "$file" "${file}.bak"
        
        # 提取包名和导入
        package_line=$(grep -m1 "^package" "$file")
        imports=$(grep "^import" "$file" | grep -v "import lombok.AllArgsConstructor" | grep -v "import lombok.Getter" | tr '\n' '#' | sed 's/#/\\n/g')
        
        # 从文件名中提取枚举类名
        class_name=$(basename "$file" .java)
        
        # 尝试提取枚举常量
        enum_constants=$(grep -A 100 "public enum" "$file" | grep -m1 -B 100 ";" | grep -v "public enum" | grep -v ";" | grep -E "^[[:space:]]+[A-Z_]+" | sed 's/^[ \t]*/    /')
        
        # 如果没有找到常量，则设置默认常量
        if [ -z "$enum_constants" ]; then
            enum_constants="    UNKNOWN(\"unknown\", \"未知\"),\n    DEFAULT(\"default\", \"默认\")"
        fi
        
        # 创建标准枚举模板
        cat > "$file" << EOF
$package_line

import lombok.Getter;
import lombok.AllArgsConstructor;
$imports

@Getter
@AllArgsConstructor
public enum $class_name {
$enum_constants;

    private final String code;
    private final String desc;
}
EOF
        info "已修复枚举类: $file"
    done
    
    # 修复bean包下的枚举类
    BEAN_ENUM_FILES=$(find chat-tag-system-api/src/main/java/com/ke/chat/tag/api/bean -name "*Enum.java")
    
    for file in $BEAN_ENUM_FILES; do
        info "正在处理Bean包下的枚举类: $file"
        
        # 创建备份
        cp "$file" "${file}.bak"
        
        # 提取包名和导入
        package_line=$(grep -m1 "^package" "$file")
        imports=$(grep "^import" "$file" | grep -v "import lombok.AllArgsConstructor" | grep -v "import lombok.Getter" | tr '\n' '#' | sed 's/#/\\n/g')
        
        # 从文件名中提取枚举类名
        class_name=$(basename "$file" .java)
        
        # 尝试提取枚举常量
        enum_constants=$(grep -A 100 "public enum" "$file" | grep -m1 -B 100 ";" | grep -v "public enum" | grep -v ";" | grep -E "^[[:space:]]+[A-Z_]+" | sed 's/^[ \t]*/    /')
        
        # 如果没有找到常量，则设置默认常量
        if [ -z "$enum_constants" ]; then
            enum_constants="    TEXT(\"text\", \"文本\"),\n    IMAGE(\"image\", \"图片\"),\n    FILE(\"file\", \"文件\")"
        fi
        
        # 创建标准枚举模板
        cat > "$file" << EOF
$package_line

import lombok.Getter;
import lombok.AllArgsConstructor;
$imports

@Getter
@AllArgsConstructor
public enum $class_name {
$enum_constants;

    private final String code;
    private final String desc;
}
EOF
        info "已修复枚举类: $file"
    done
}

# 3. 修复CustomDateDeserializer类中缺少的Logger
fix_custom_date_deserializer() {
    info "修复CustomDateDeserializer类中缺少的Logger..."
    
    FILE="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/vo/partner/CustomDateDeserializer.java"
    
    if [ -f "$FILE" ]; then
        # 创建备份
        cp "$FILE" "${FILE}.bak"
        
        # 提取包名
        package_line=$(grep -m1 "^package" "$FILE")
        
        # 创建临时文件
        temp_file=$(mktemp)
        
        # 添加Logger导入和字段
        cat > "$temp_file" << EOF
$package_line

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

$(grep -v "^package" "$FILE" | sed '/^public class/a \    private static final Logger LOGGER = LoggerFactory.getLogger(CustomDateDeserializer.class);')
EOF
        
        # 将临时文件内容写回原文件
        mv "$temp_file" "$FILE"
        
        info "已修复CustomDateDeserializer类"
    else
        warn "未找到CustomDateDeserializer类文件"
    fi
}

# 4. 修复DTO类问题
fix_dto_classes() {
    info "修复DTO类问题..."
    
    # 修复ImageScoreTaskUploadDTO
    DTO_FILE="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/dto/task/image_score/ImageScoreTaskUploadDTO.java"
    
    if [ -f "$DTO_FILE" ]; then
        # 创建备份
        cp "$DTO_FILE" "${DTO_FILE}.bak"
        
        # 修改注解行，去掉重复的@Nullable
        sed -i '' 's/@javax.annotation.Nullable @javax.annotation.Nullable/@javax.annotation.Nullable/g' "$DTO_FILE"
        
        info "已修复ImageScoreTaskUploadDTO类"
    else
        warn "未找到ImageScoreTaskUploadDTO类文件"
    fi
}

# 执行所有修复函数
fix_logger
fix_enums
fix_custom_date_deserializer
fix_dto_classes

info "所有修复完成。现在可以尝试重新构建项目。" 