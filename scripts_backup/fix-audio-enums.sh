#!/bin/bash

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

log "修复 audio 包下的枚举类..."

# 修复 ResponseStatusEnum
file="chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/audio/ResponseStatusEnum.java"
if [ -f "$file" ]; then
    log "修复 $file"
    # 查看枚举定义
    cat "$file"
    # 添加构造函数
    sed -i '' '/public enum ResponseStatusEnum/a\
    private final String code;\
    private final String message;\
\
    ResponseStatusEnum(String code, String message) {\
        this.code = code;\
        this.message = message;\
    }\
\
    public String getCode() {\
        return code;\
    }\
\
    public String getMessage() {\
        return message;\
    }' "$file"
    log "已修复 ResponseStatusEnum"
fi

log "audio 包下的枚举类修复完成" 