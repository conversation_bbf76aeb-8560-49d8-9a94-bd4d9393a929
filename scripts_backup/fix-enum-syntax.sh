#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 需要手动修复的枚举文件列表
ENUM_FILES=(
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/audio/ResponseStatusEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/qyqk/BusinessEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/application/ApplicationBellaSourceEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/tool/ToolResponseModeEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/qyqk/RecordProgressEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/qyqk/ConfigDataTypeEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/audio/ItemStatusEnum.java"
    "chat-tag-system-api/src/main/java/com/ke/chat/tag/api/enums/audio/ItemRoleEnum.java"
)

info "开始修复枚举类语法问题..."

for enum_file in "${ENUM_FILES[@]}"; do
    info "处理文件: $enum_file"
    
    # 创建备份
    cp "$enum_file" "${enum_file}.bak"
    
    # 提取文件的包名、导入和注解部分
    package_line=$(grep -m1 "^package" "$enum_file")
    imports=$(grep "^import" "$enum_file")
    annotations=$(grep "^@" "$enum_file")
    enum_decl=$(grep -m1 "public enum" "$enum_file")
    enum_name=$(echo "$enum_decl" | sed -E 's/public enum ([A-Za-z0-9_]+).*/\1/')
    
    # 提取枚举常量
    constant_lines=$(grep -A20 "public enum" "$enum_file" | grep -B20 "private" | grep -v "public enum" | grep -v "private")
    
    # 移除最后一个逗号，替换为分号
    last_constant=$(echo "$constant_lines" | tail -n1)
    other_constants=$(echo "$constant_lines" | grep -v "$last_constant")
    
    # 创建新的枚举类内容
    new_content="$package_line

$imports

$annotations
public enum $enum_name {
    $other_constants
    $last_constant;
    
    private String code;
    private String desc;
    
    ${enum_name}(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    ${enum_name}(String code) {
        this.code = code;
        this.desc = code;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
}
"
    
    # 写入新内容
    echo "$new_content" > "$enum_file"
    info "已修复文件: $enum_file"
done

info "枚举类语法修复完成" 