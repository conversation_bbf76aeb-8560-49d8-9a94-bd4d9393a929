package com.ke.chat.tag.dao.entity.enums;

import lombok.Getter;
import lombok.AllArgsConstructor;
/**
 * <AUTHOR>
 * @date 2024/08/08
 */
@Getter
@AllArgsConstructorpublic enum ThreadSummaryJobStatusEnum {
    /**
     * 未开始
     */
    NOT_START("NOT_START", "未开始"),
    /**
     * 进行中
     */
    PROCESSING("PROCESSING", "进行中"),
    /**
     * 已完成
     */
    FINISHED("FINISHED", "已完成");

    private final String code;
    private final String description;

    ThreadSummaryJobStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
}
