#!/bin/bash

# 快速修复和部署脚本
# 这个脚本会尝试解决最关键的问题并部署项目

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

info "=== 快速修复和部署Java项目 ==="

# 检查Java版本
JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | cut -d'.' -f1)
info "当前Java版本: $JAVA_VERSION"

if [ "$JAVA_VERSION" -gt 11 ]; then
    warn "当前Java版本($JAVA_VERSION)可能与项目不兼容"
    warn "建议使用Java 8或Java 11"
    
    # 尝试查找其他Java版本
    if command -v /usr/libexec/java_home &> /dev/null; then
        info "查找可用的Java版本..."
        /usr/libexec/java_home -V 2>&1 | grep -E "1\.[8-9]|1[0-1]\." || true
    fi
fi

# 尝试使用项目中已有的构建脚本
info "尝试使用项目的构建脚本..."

# 检查是否有可用的构建脚本
BUILD_SCRIPTS=("build-and-run.sh" "local-deploy.sh" "simple-build.sh")
SELECTED_SCRIPT=""

for script in "${BUILD_SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        SELECTED_SCRIPT="$script"
        break
    fi
done

if [ -n "$SELECTED_SCRIPT" ]; then
    info "找到构建脚本: $SELECTED_SCRIPT"
    chmod +x "$SELECTED_SCRIPT"
    
    info "执行构建脚本..."
    if ./"$SELECTED_SCRIPT"; then
        info "构建成功！"
        exit 0
    else
        warn "构建脚本执行失败，尝试其他方法..."
    fi
fi

# 如果构建脚本失败，尝试直接Maven构建
info "尝试直接Maven构建..."

# 设置Maven参数
export MAVEN_OPTS="-Dmaven.resolver.transport.http.blocker.disabled=true -Dmaven.wagon.http.ssl.insecure=true"

# 尝试只编译不打包
info "尝试编译项目..."
if mvn clean compile -DskipTests -Dmaven.resolver.transport.http.blocker.disabled=true; then
    info "编译成功，尝试打包..."
    if mvn package -DskipTests -Dmaven.resolver.transport.http.blocker.disabled=true; then
        info "打包成功！"
        
        # 查找JAR包
        JAR_FILE=$(find . -name "*.jar" -path "*/target/*" | grep -v "sources" | grep -v "javadoc" | head -1)
        if [ -n "$JAR_FILE" ]; then
            info "找到JAR包: $JAR_FILE"
            
            # 询问是否运行
            read -p "是否运行应用? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                info "启动应用..."
                java -Xms1024m -Xmx2048m -jar "$JAR_FILE" --spring.profiles.active=passport,test
            else
                info "可以使用以下命令运行应用:"
                echo "java -Xms1024m -Xmx2048m -jar $JAR_FILE --spring.profiles.active=passport,test"
            fi
        else
            warn "未找到JAR包"
        fi
    else
        error "打包失败"
    fi
else
    error "编译失败"
    
    info "建议的解决方案:"
    echo "1. 安装Java 8或Java 11:"
    echo "   brew install openjdk@8"
    echo "   export JAVA_HOME=\$(/usr/libexec/java_home -v 1.8)"
    echo ""
    echo "2. 或者使用Docker构建:"
    echo "   docker run --rm -v \$(pwd):/app -w /app openjdk:8-jdk mvn clean package -DskipTests"
    echo ""
    echo "3. 手动修复Lombok兼容性问题"
fi

info "脚本执行完成"
