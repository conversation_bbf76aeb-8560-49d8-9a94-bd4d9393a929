package com.ke.chat.tag.api.enums.tool;

import lombok.Getter;
import lombok.AllArgsConstructor;


@Getter
@AllArgsConstructor
public enum ToolMarketTypeEnum {
    AI_AGENT("ai_agent", "AI助手", "ai_agent_icon.svg", "AI问答助手"),
    FUNCTION("function", "函数", "function_icon.svg", "实用函数工具"),
    KNOWLEDGE_BASE("knowledge_base", "知识库", "knowledge_base_icon.svg", "结构化知识库"),
    CALL_APP("call_app", "调用应用", "call_app_icon.svg", "对接内外部应用"),
    DATA_GRAPH("data_graph", "数据图表", "data_graph_icon.svg", "可视化数据分析"),
    PLUGIN("plugin", "插件", "plugin_icon.svg", "扩展功能插件"),
    UI("ui", "用户界面", "ui_icon.svg", "界面交互组件"),
    OTHER("other", "其他", "other_icon.svg", "其他类型工具"),
    UNKNOWN("unknown", "未知", "", ""),;

    private final String code;
    private final String desc;
}
