package com.ke.chat.tag.api.enums;

import lombok.Getter;
import lombok.AllArgsConstructor;
import com.ke.chat.tag.api.dto.practice.ChallengeButtonDTO;\nimport software.amazon.awssdk.utils.StringUtils;\nimport java.util.ArrayList;\nimport java.util.List;\nimport java.util.Objects;\n

@Getter
@AllArgsConstructor
public enum ChallengeWayEnum {
    UNKNOWN("unknown", "未知"),;

    private final String code;
    private final String desc;
}
