package com.ke.chat.tag.api.enums.practice;

import lombok.Getter;
import lombok.AllArgsConstructor;
import com.ke.chat.tag.api.dto.practice.EvaluationManagerDTO;\nimport com.ke.chat.tag.api.dto.practice.FeedbackManagerDTO;\nimport com.ke.chat.tag.api.dto.practice.PromptManagerDTO;\nimport com.ke.chat.tag.api.dto.practice.ResistanceProblemDTO;\nimport com.ke.chat.tag.api.dto.practice.ScoreManagerDTO;\nimport com.ke.chat.tag.api.dto.practice.ScriptDescriptionDTO;\nimport com.ke.chat.tag.api.dto.practice.TtsAudioManagerDTO;\nimport com.ke.chat.tag.api.dto.practice.challenge.ChallengeDetailCheck;\n

@Getter
@AllArgsConstructor
public enum DetailDataEnum {
    UNKNOWN("unknown", "未知"),;

    private final String code;
    private final String desc;
}
