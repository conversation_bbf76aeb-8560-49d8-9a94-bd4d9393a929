package com.ke.chat.tag.api.enums.application;

import lombok.Getter;
import lombok.AllArgsConstructor;
import java.time.DayOfWeek;\nimport java.time.LocalDateTime;\nimport java.time.LocalTime;\nimport java.util.Map;\nimport java.util.function.Function;\nimport java.util.stream.Collectors;\nimport java.util.stream.Stream;\n

@Getter
@AllArgsConstructor
public enum ApplicationStatisticsTimeEnum {
    UNKNOWN("unknown", "未知"),;

    private final String code;
    private final String desc;
}
