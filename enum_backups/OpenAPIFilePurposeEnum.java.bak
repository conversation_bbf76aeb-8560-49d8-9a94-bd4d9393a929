package com.ke.chat.tag.api.enums;

import lombok.Getter;
import lombok.AllArgsConstructor;
import com.ke.chat.tag.api.exception.openapi.InvalidRequestError;\nimport com.ke.chat.tag.api.util.JsonUtil;\nimport java.util.List;\nimport java.util.stream.Collectors;\nimport java.util.stream.Stream;\n

@Getter
@AllArgsConstructor
public enum OpenAPIFilePurposeEnum {
    UNKNOWN("unknown", "未知"),;

    private final String code;
    private final String desc;
}
