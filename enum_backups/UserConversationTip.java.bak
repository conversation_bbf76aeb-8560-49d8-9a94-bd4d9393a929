package com.ke.chat.tag.api.enums.application;

import lombok.Getter;
import lombok.AllArgsConstructor;
import com.ke.chat.tag.api.util.DateUtil;\nimport java.time.LocalDate;\nimport java.time.LocalDateTime;\nimport java.time.ZoneOffset;\nimport java.util.Objects;\n

@Getter
@AllArgsConstructor
public enum UserConversationTip {
    UNKNOWN("unknown", "未知"),;

    private final String code;
    private final String desc;
}
